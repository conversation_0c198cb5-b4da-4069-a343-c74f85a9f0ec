import { getCurrentUser, getCustomerProfile } from "@/lib/auth/customerAuth";
import { supabase } from "@/lib/supabase";
import { cleanAddressData } from "@/backend/supabase/utils/addressValidation";
import { router } from "expo-router";
import { useForm, FormProvider } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { customerProfileCompletionSchema } from "@/src/utils/validationSchemas";
import { useLocationPermission } from "@/src/hooks/useLocationPermission";
import React, { useRef, useState, useEffect } from "react";
import {
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  View,
  TouchableOpacity,
  Text,
  Alert,
  Animated,
  Easing,
} from "react-native";
import { Loader2 } from "lucide-react-native";
import {
  SafeAreaView,
  useSafeAreaInsets,
} from "react-native-safe-area-context";
import { useTheme } from "@/src/hooks/useTheme";
import { completeCustomerProfileStyles } from "@/styles/auth/complete-customer-profile-styles";
import {
  uploadAvatarImage,
  openCameraForAvatar,
  openGalleryForAvatar,
} from "@/backend/supabase/services/storage/avatarUploadService";
import { compressImageModerateClient } from "@/src/utils/client-image-compression";

import { usePincodeDetails } from "@/src/hooks/usePincodeDetails";
import { useToast } from "@/src/components/ui/Toast";
import { useAlertDialog } from "@/src/components/providers/AlertProvider";
import { useAuth } from "@/src/contexts/AuthContext";
import NetInfo from "@react-native-community/netinfo";
import { FormData, InitialFormData } from "@/src/types/profile";
import LoadingOverlay from "@/src/components/common/LoadingOverlay";
import AvatarUploadSection from "@/src/components/profile/AvatarUploadSection";
import PersonalInformationSection from "@/src/components/profile/PersonalInformationSection";
import AddressInformationSection from "@/src/components/profile/AddressInformationSection";
import ImagePickerBottomSheet, {
  ImagePickerBottomSheetRef,
} from "@/src/components/pickers/ImagePickerBottomSheet";
import LocalityBottomSheetPicker, {
  LocalityBottomSheetPickerRef,
} from "@/src/components/pickers/LocalityBottomSheetPicker";

/**
 * Complete Customer Profile Screen
 * Dedicated screen for customers only to complete their profile with address details
 * No back button - user must complete profile to proceed
 * Better UX than redirecting to general profile page
 */

const CompleteCustomerProfileScreen = () => {
  const theme = useTheme();
  const { isDark } = theme;
  const toast = useToast();
  const { confirm, error: showError } = useAlertDialog();
  const { refreshProfileStatus } = useAuth(); // Get AuthContext refresh method
  const insets = useSafeAreaInsets(); // Add safe area insets
  const styles = completeCustomerProfileStyles(theme);

  const [initialFormData, setInitialFormData] = useState<InitialFormData>({
    name: "",
    address: "",
    pincode: "",
    city: "",
    state: "",
    locality: "",
    latitude: undefined,
    longitude: undefined,
    avatarUri: "",
  });

  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingProfile, setIsLoadingProfile] = useState(true);
  const [isCompressingImage, setIsCompressingImage] = useState(false); // Separate state for image compression

  const spinValue = useRef(new Animated.Value(0)).current;
  const spinAnimationRef = useRef<Animated.CompositeAnimation | null>(null);

  useEffect(() => {
    if (isLoading) {
      // Start the spinning animation when loading
      spinValue.setValue(0);
      spinAnimationRef.current = Animated.loop(
        Animated.timing(spinValue, {
          toValue: 1,
          duration: 1000, // 1 second for one full rotation
          easing: Easing.linear,
          useNativeDriver: true, // Use native driver for better performance
        })
      );
      spinAnimationRef.current.start();
    } else {
      // Stop the animation when not loading
      if (spinAnimationRef.current) {
        spinAnimationRef.current.stop();
        spinAnimationRef.current = null;
      }
      spinValue.setValue(0);
    }

    // Cleanup function to stop animation on unmount
    return () => {
      if (spinAnimationRef.current) {
        spinAnimationRef.current.stop();
        spinAnimationRef.current = null;
      }
    };
  }, [isLoading, spinValue]);

  const spin = spinValue.interpolate({
    inputRange: [0, 1],
    outputRange: ["0deg", "360deg"],
  });

  const [gpsDetectedLocality, setGpsDetectedLocality] = useState<string | null>(
    null
  );
  const [hasGpsCoordinates, setHasGpsCoordinates] = useState(false);

  // Location permission hook
  const { permission: locationPermission } = useLocationPermission();

  // React Hook Form setup
  const formMethods = useForm<FormData>({
    resolver: yupResolver(customerProfileCompletionSchema) as any,
    defaultValues: {
      name: "",
      address: "",
      pincode: "",
      city: "",
      state: "",
      locality: "",
      avatarUri: "",
      // latitude and longitude will be set when GPS location is detected
    },
    mode: "onChange",
    reValidateMode: "onChange",
  });

  const {
    control,
    handleSubmit: handleFormSubmit,
    formState,
    formState: { isValid },
    reset: resetForm,
    watch,
    setValue,
    trigger,
  } = formMethods;

  const scrollViewRef = useRef<ScrollView>(null);
  const imagePickerRef = useRef<ImagePickerBottomSheetRef>(null);
  const localityPickerRef = useRef<LocalityBottomSheetPickerRef>(null);

  // Use the pincode details hook
  const {
    isPincodeLoading,
    availableLocalities,
    handlePincodeChange: fetchPincodeDetails,
  } = usePincodeDetails({
    onPincodeChange: (details) => {
      if (details && details.city && details.state) {
        setValue("city", details.city);
        setValue("state", details.state);
        setValue("locality", ""); // Reset locality when pincode changes

        setInitialFormData((prev) => ({
          ...prev,
          pincode: watch("pincode"), // Preserve the current pincode
          address: watch("address"), // Preserve the current address
          city: details.city,
          state: details.state,
          locality: "", // Reset locality when pincode changes
          latitude: watch("latitude"), // Preserve the GPS coordinates
          longitude: watch("longitude"), // Preserve the GPS coordinates
        }));
      }
    },
  });

  // Auto-select GPS-detected locality when localities are fetched
  useEffect(() => {
    if (gpsDetectedLocality && availableLocalities.length > 0) {
      // Add a small delay to ensure this runs after onPincodeChange callback
      setTimeout(() => {
        // Find the matching locality in the fetched list
        const matchingLocality = availableLocalities.find(
          (locality) =>
            locality.toLowerCase().trim() ===
            gpsDetectedLocality.toLowerCase().trim()
        );

        if (matchingLocality) {
          setValue("locality", matchingLocality);
          setGpsDetectedLocality(null); // Clear the stored value

          // Trigger validation after auto-selection
          setTimeout(() => {
            trigger();
          }, 100);
        } else {
        }
      }, 50); // Small delay to ensure onPincodeChange completes first
    }
  }, [availableLocalities, gpsDetectedLocality, setValue, trigger]);

  const loadExistingProfile = React.useCallback(async () => {
    try {
      const user = await getCurrentUser();
      if (!user) {
        router.replace("/(auth)/login");
        return;
      }

      const { data: profile } = await getCustomerProfile(user.id);
      if (profile) {
        setInitialFormData((prev) => ({
          ...prev,
          name: profile.name || "",
          address: profile.address || "",
          pincode: profile.pincode || "",
          city: profile.city || "",
          state: profile.state || "",
          locality: profile.locality || "",
          latitude: profile.latitude || prev.latitude,
          longitude: profile.longitude || prev.longitude,
          avatarUri: profile.avatar_url || "",
        }));

        // Check if GPS coordinates are already available
        if (profile.latitude && profile.longitude) {
          setHasGpsCoordinates(true);
        }
      }
    } catch (error) {
      console.error("Error loading profile:", error);
      showError("Error", "Failed to load profile information");
    } finally {
      setIsLoadingProfile(false);
    }
  }, [showError]);

  React.useEffect(() => {
    loadExistingProfile();
  }, [loadExistingProfile]);

  // Reset form when initial data changes, but preserve user-entered values
  useEffect(() => {
    const currentAvatarUri = watch("avatarUri");
    const currentName = watch("name");
    const currentAddress = watch("address");

    const preservedAvatarUri = currentAvatarUri || initialFormData.avatarUri;
    const preservedName = currentName || initialFormData.name;
    const preservedAddress = currentAddress || initialFormData.address;

    resetForm({
      ...initialFormData,
      // Preserve user-entered values to avoid losing them during GPS location detection
      name: preservedName,
      address: preservedAddress,
      avatarUri: preservedAvatarUri,
    });
  }, [initialFormData, resetForm, watch]);

  // Handle pincode input changes with GPS lookup
  const handlePincodeInputChange = (value: string) => {
    const cleanedPincode = value.replace(/\D/g, "").substring(0, 6);
    setValue("pincode", cleanedPincode);
    if (cleanedPincode.length === 6) {
      fetchPincodeDetails(cleanedPincode);
    }
  };

  // Handle GPS location detected
  const handleLocationDetected = (latitude: number, longitude: number) => {
    setValue("latitude", latitude);
    setValue("longitude", longitude);
    setHasGpsCoordinates(true);
    // Trigger validation to ensure form recognizes the coordinates
    trigger(["latitude", "longitude"]);
  };

  // Handle address detected from GPS
  const handleAddressDetected = (address: {
    pincode: string;
    city: string;
    state: string;
    locality: string;
  }) => {
    // Auto-populate form fields with detected address
    setValue("pincode", address.pincode);
    setValue("city", address.city);
    setValue("state", address.state);

    // Store the GPS-detected locality name for auto-selection after localities are fetched
    setGpsDetectedLocality(address.locality);

    // Update initial form data to reflect the changes, preserving the current name
    setInitialFormData((prev) => ({
      ...prev,
      name: watch("name"), // Preserve the current name from the form
      pincode: address.pincode,
      address: watch("address"), // Preserve the current address
      city: address.city,
      state: address.state,
      locality: "", // Will be set after localities are fetched and matched
      latitude: watch("latitude"), // Preserve the GPS coordinates
      longitude: watch("longitude"), // Preserve the GPS coordinates
    }));

    // Fetch localities for the detected pincode - this will trigger auto-selection
    fetchPincodeDetails(address.pincode);

    // Trigger validation to ensure form recognizes all auto-filled fields
    setTimeout(() => {
      trigger();
    }, 200);
  };

  // Handle locality selection
  const handleLocalitySelect = (locality: string) => {
    setValue("locality", locality);
    // Trigger validation to ensure form recognizes the locality
    setTimeout(() => {
      trigger();
    }, 100);
  };

  // Handle camera selection
  const handleCameraSelection = async () => {
    imagePickerRef.current?.dismiss(); // Dismiss bottom sheet
    try {
      const result = await openCameraForAvatar();
      if (result && !result.canceled && result.assets && result.assets[0]) {
        const imageUri = result.assets[0].uri;
        // Check file size (15MB limit)
        const response = await fetch(imageUri);
        const blob = await response.blob();
        if (blob.size > 15 * 1024 * 1024) {
          toast.error("Error", "File size must be less than 15MB.");
          return;
        }
        await handleImageSelect(imageUri);
      }
    } catch (error) {
      console.error("Camera error:", error);
      toast.error("Error", "Failed to take photo. Please try again.");
    }
  };

  // Handle gallery selection
  const handleGallerySelection = async () => {
    imagePickerRef.current?.dismiss(); // Dismiss bottom sheet
    try {
      const result = await openGalleryForAvatar();
      if (result && !result.canceled && result.assets && result.assets[0]) {
        const imageUri = result.assets[0].uri;
        // Check file size (15MB limit)
        const response = await fetch(imageUri);
        const blob = await response.blob();
        if (blob.size > 15 * 1024 * 1024) {
          toast.error("Error", "File size must be less than 15MB.");
          return;
        }
        await handleImageSelect(imageUri);
      }
    } catch (error) {
      console.error("Gallery error:", error);
      toast.error("Error", "Failed to select image. Please try again.");
    }
  };

  // Handle image selection with compression
  const handleImageSelect = async (imageUri: string) => {
    try {
      setIsCompressingImage(true); // Use separate loading state to prevent layout shift
      const user = await getCurrentUser();
      if (!user) {
        router.replace("/(auth)/login");
        return;
      }

      // Get original file size
      const response = await fetch(imageUri);
      const blob = await response.blob();
      const originalSize = blob.size;

      // Use Sharp-like compression logic targeting 50KB
      const compressionResult = await compressImageModerateClient(
        imageUri,
        originalSize,
        {
          targetSizeKB: 45, // Target 45KB to ensure we stay under 50KB
          maxDimension: 400,
          quality: 0.7,
          format: "webp",
        }
      );

      // Validate compression result
      if (!compressionResult.uri) {
        throw new Error("Compression did not return a valid URI");
      }

      // Store compressed image URI for upload on form submission
      setValue("avatarUri", compressionResult.uri);
    } catch (error) {
      console.error("Image compression failed:", error);
      toast.error(
        "Compression Failed",
        "Failed to compress image. Please try again."
      );
    } finally {
      setIsCompressingImage(false); // Reset separate loading state
    }
  };

  const handleSubmit = async (values: FormData) => {
    // Check network connectivity first
    const networkState = await NetInfo.fetch();
    if (!networkState.isConnected) {
      toast.error(
        "No Internet Connection",
        "Please check your internet connection and try again."
      );
      return;
    }

    setIsLoading(true);
    try {
      const user = await getCurrentUser();
      if (!user) {
        router.replace("/(auth)/login");
        return;
      }

      // Upload avatar if selected (it should already be compressed from handleImageSelect)
      let avatarUrl = null;
      if (values.avatarUri) {
        const uploadResult = await uploadAvatarImage(values.avatarUri, user.id);
        if (uploadResult.success && uploadResult.url) {
          avatarUrl = uploadResult.url;
        } else {
          toast.warning(
            "Avatar Upload Failed",
            "Profile saved but avatar upload failed. You can update it later."
          );
        }
      }

      // Clean address data
      const addressData = cleanAddressData({
        pincode: values.pincode,
        state: values.state,
        city: values.city,
        locality: values.locality,
        address: values.address,
      });

      // First update name in auth.users table (full_name in user_metadata)
      // The database trigger will automatically sync this to customer_profiles table
      const { error: authUpdateError } = await supabase.auth.updateUser({
        data: { full_name: values.name.trim() },
      });

      if (authUpdateError) {
        console.error("Error updating auth user metadata:", authUpdateError);
        toast.error(
          "Profile Update Failed",
          "Failed to update profile name. Please try again."
        );
        return;
      }

      // Update customer profile (without name field - it will be synced by trigger)
      const profileData = {
        id: user.id,
        ...addressData,
        latitude: values.latitude,
        longitude: values.longitude,
        updated_at: new Date().toISOString(),
        ...(avatarUrl && { avatar_url: avatarUrl }),
      };

      const { error, data } = await supabase
        .from("customer_profiles")
        .upsert(profileData)
        .select(); // Get the updated data to confirm success

      if (error) {
        console.error("Error updating profile:", error);
        toast.error(
          "Profile Update Failed",
          "Failed to update profile. Please try again."
        );
        return;
      }

      // Verify the data was actually updated
      if (!data || data.length === 0) {
        toast.error(
          "Profile Update Failed",
          "Profile update was not confirmed. Please try again."
        );
        return;
      }

      // Show success toast
      toast.success(
        "Profile Completed!",
        "Your profile has been completed successfully!"
      );

      // Wait a moment for database replication
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Refresh BOTH profile validation AND AuthContext
      // AuthGuard will automatically redirect once it sees the updated profile status
      await refreshProfileStatus(); // This updates AuthContext which AuthGuard uses

      // No manual redirect needed - AuthGuard will handle the redirect automatically
    } catch (error) {
      console.error("Error submitting profile:", error);

      // Check if it's a network error
      const networkState = await NetInfo.fetch();
      if (!networkState.isConnected) {
        toast.error(
          "Connection Lost",
          "Your internet connection was lost. Please try again when connected."
        );
      } else {
        toast.error(
          "Unexpected Error",
          "An unexpected error occurred. Please try again."
        );
      }
    } finally {
      setIsLoading(false);
    }
  };

  const backgroundColor = isDark ? "#000000" : "#FFFFFF";
  const textColor = isDark ? "#FFFFFF" : "#000000";
  const borderColor = isDark ? "#374151" : "#E5E7EB";
  const goldColor = "#D4AF37";

  if (isLoadingProfile) {
    return (
      <LoadingOverlay textColor={textColor} backgroundColor={backgroundColor} />
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]}>
      <KeyboardAvoidingView
        style={styles.keyboardAvoid}
        behavior={Platform.OS === "ios" ? "padding" : "height"}
      >
        <ScrollView
          ref={scrollViewRef}
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
          nestedScrollEnabled={true}
        >
          <FormProvider {...formMethods}>
            {/* Form */}
            <View style={styles.form}>
              <AvatarUploadSection
                avatarUri={watch("avatarUri") || ""}
                isLoading={isCompressingImage} // Use separate loading state to prevent layout shift
                theme={theme}
                imagePickerRef={imagePickerRef}
              />

              <PersonalInformationSection
                control={control}
                textColor={textColor}
                borderColor={borderColor}
                styles={styles}
              />

              <AddressInformationSection
                control={control}
                textColor={textColor}
                borderColor={borderColor}
                styles={styles}
                isDark={isDark}
                isPincodeLoading={isPincodeLoading}
                availableLocalities={availableLocalities}
                locationPermission={locationPermission}
                hasGpsCoordinates={hasGpsCoordinates}
                handlePincodeInputChange={handlePincodeInputChange}
                handleLocationDetected={handleLocationDetected}
                handleAddressDetected={handleAddressDetected}
                handleLocalitySelect={handleLocalitySelect}
                toast={toast}
                trigger={trigger}
                localityPickerRef={localityPickerRef}
              />
            </View>
          </FormProvider>
        </ScrollView>

        {/* Submit Button */}
        <View
          style={[
            styles.footer,
            {
              borderTopColor: borderColor,
              backgroundColor: backgroundColor, // Fix transparent background
              paddingBottom: Math.max(insets.bottom, 16), // Add safe area bottom padding
            },
          ]}
        >
          <TouchableOpacity
            style={[
              styles.submitButton,
              { backgroundColor: goldColor },
              isLoading && styles.submitButtonDisabled,
            ]}
            onPress={async () => {
              // Always trigger validation first
              const isFormValid = await trigger();
              if (isFormValid) {
                // If valid, proceed with form submission
                handleFormSubmit(handleSubmit)();
              } else {
                // If invalid, show validation errors
                const errors = formState.errors;
                const errorFields = Object.keys(errors);
                if (errorFields.length > 0) {
                  const firstError =
                    errors[errorFields[0] as keyof typeof errors];
                  toast.error(
                    "Please fix the following errors:",
                    firstError?.message || `${errorFields[0]} is required`
                  );
                }
              }
            }}
            disabled={isLoading}
          >
            {isLoading ? (
              <View style={{ flexDirection: "row", alignItems: "center" }}>
                <Animated.View style={{ transform: [{ rotate: spin }] }}>
                  <Loader2 size={20} color="white" style={{ marginRight: 8 }} />
                </Animated.View>
                <Text style={styles.submitButtonText}>Saving...</Text>
              </View>
            ) : (
              <Text style={styles.submitButtonText}>Complete Profile</Text>
            )}
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>

      {/* Image Picker Bottom Sheet - positioned at screen level */}
      <ImagePickerBottomSheet
        ref={imagePickerRef}
        onCameraPress={handleCameraSelection}
        onGalleryPress={handleGallerySelection}
        title="Select Profile Picture"
        cameraLabel="Take Photo"
        galleryLabel="Choose from Gallery"
      />

      {/* Locality Picker Bottom Sheet - positioned at screen level */}
      <LocalityBottomSheetPicker
        ref={localityPickerRef}
        localities={availableLocalities}
        selectedLocality={watch("locality")}
        onLocalitySelect={handleLocalitySelect}
        placeholder="Select your locality"
      />
    </SafeAreaView>
  );
};

export default CompleteCustomerProfileScreen;
