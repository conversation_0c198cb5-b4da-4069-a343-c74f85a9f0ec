ninja: Entering directory `C:\web-app\dukancard-app\node_modules\react-native-screens\android\.cxx\Debug\1p33f94i\x86'
[1/6] Building CXX object CMakeFiles/rnscreens.dir/C_/web-app/dukancard-app/node_modules/react-native-screens/cpp/RNScreensTurboModule.cpp.o
[2/6] Building CXX object CMakeFiles/rnscreens.dir/src/main/cpp/jni-adapter.cpp.o
[3/6] Building CXX object CMakeFiles/rnscreens.dir/C_/web-app/dukancard-app/node_modules/react-native-screens/cpp/RNSScreenRemovalListener.cpp.o
[4/6] Building CXX object CMakeFiles/rnscreens.dir/src/main/cpp/OnLoad.cpp.o
[5/6] Building CXX object CMakeFiles/rnscreens.dir/src/main/cpp/NativeProxy.cpp.o
[6/6] Linking CXX shared library ..\..\..\..\build\intermediates\cxx\Debug\1p33f94i\obj\x86\librnscreens.so
