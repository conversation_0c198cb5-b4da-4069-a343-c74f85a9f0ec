import React from "react";
import { render, act } from "@testing-library/react-native";
import { BusinessProfileStats } from "./BusinessProfileStats";
import { realtimeService } from "@/backend/supabase/services/realtime/realtimeService";
import { renderWithProviders } from "@/__tests__/utils/testUtils";
import { createMockBusinessProfile } from "@/__tests__/__mocks__/mockData";

jest.mock("@/backend/supabase/services/realtime/realtimeService");
const mockRealtimeService = realtimeService as jest.Mocked<
  typeof realtimeService
>;

const mockProfile = createMockBusinessProfile({
  business_name: "Test Business",
  total_likes: 1000,
  total_subscriptions: 500,
  average_rating: 4.5,
});

describe("BusinessProfileStats", () => {
  it("renders initial stats correctly", () => {
    const { getByText } = renderWithProviders(
      <BusinessProfileStats
        initialProfile={mockProfile}
        userId="test-user-id"
      />
    );

    expect(getByText("1k")).toBeTruthy();
    expect(getByText("500")).toBeTruthy();
    expect(getByText("4.5")).toBeTruthy();
    expect(getByText("Likes")).toBeTruthy();
    expect(getByText("Subscribers")).toBeTruthy();
    expect(getByText("Rating")).toBeTruthy();
  });

  it("subscribes to real-time updates when the screen is focused", () => {
    renderWithProviders(
      <BusinessProfileStats
        initialProfile={mockProfile}
        userId="test-user-id"
      />
    );
    expect(mockRealtimeService.subscribeToBusinessUpdates).toHaveBeenCalledWith(
      "test-user-id",
      expect.any(Function)
    );
  });

  it("updates stats when new data is received from real-time subscription", () => {
    let updateCallback: (event: any) => void = () => {};
    mockRealtimeService.subscribeToBusinessUpdates.mockImplementation(
      (userId, callback) => {
        updateCallback = callback;
        return { id: "1", channel: {} as any, unsubscribe: jest.fn() };
      }
    );

    const { getByText } = renderWithProviders(
      <BusinessProfileStats
        initialProfile={mockProfile}
        userId="test-user-id"
      />
    );

    act(() => {
      updateCallback({
        new: {
          ...mockProfile,
          total_likes: 1500,
          total_subscriptions: 600,
          average_rating: 4.8,
        },
      });
    });

    expect(getByText("1.5k")).toBeTruthy();
    expect(getByText("600")).toBeTruthy();
    expect(getByText("4.8")).toBeTruthy();
  });

  it("unsubscribes from real-time updates on unmount", () => {
    const unsubscribe = jest.fn();
    mockRealtimeService.subscribeToBusinessUpdates.mockReturnValue({
      id: "1",
      channel: {} as any,
      unsubscribe,
    });

    const { unmount } = renderWithProviders(
      <BusinessProfileStats
        initialProfile={mockProfile}
        userId="test-user-id"
      />
    );

    unmount();
    expect(unsubscribe).toHaveBeenCalledTimes(1);
  });
});
