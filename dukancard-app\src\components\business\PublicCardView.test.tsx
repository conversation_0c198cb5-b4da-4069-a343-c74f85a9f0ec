import React from "react";
import { render, fireEvent, act } from "@testing-library/react-native";
import { Linking, Alert } from "react-native";
import PublicCardView from "./PublicCardView";
import { renderWithProviders } from "@/__tests__/utils/testUtils";
import { createMockBusinessProfile } from "@/__tests__/__mocks__/mockData";
import * as BusinessCardHooks from "@/src/hooks/useBusinessCardData";
import * as BusinessInteractionsHooks from "@/src/hooks/useBusinessInteractions";
import * as LocationContext from "@/src/contexts/LocationContext";
import { BusinessDiscoveryData } from "@/backend/supabase/services/business/businessDiscovery";

jest.mock("react-native/Libraries/Linking/Linking", () => ({
  openURL: jest.fn(),
}));

jest.mock("@/src/hooks/useBusinessCardData");
jest.mock("@/src/hooks/useBusinessInteractions");
jest.mock("@/src/contexts/LocationContext");

const mockBusinessData: BusinessDiscoveryData = {
  ...createMockBusinessProfile({
    id: "business-1",
    business_name: "Test Cafe",
    business_slug: "test-cafe",
    latitude: 12.34,
    longitude: 56.78,
    pincode: "12345",
  }),
  user_plan: "free",
};

describe("PublicCardView", () => {
  const mockHandleLikePress = jest.fn();
  const mockHandleSubscribePress = jest.fn();
  const mockHandleReviewPress = jest.fn((callback) => callback());

  beforeEach(() => {
    (BusinessCardHooks.useBusinessCardData as jest.Mock).mockReturnValue({
      cardData: {
        products: [{ id: "prod-1", name: "Coffee", price: 5 }],
        gallery: [{ id: "img-1", url: "http://example.com/img.png" }],
        reviews: [{ id: "rev-1", rating: 5, comment: "Great!" }],
        reviewStats: { average: 5, count: 1 },
      },
      loadingCardData: false,
      adData: null,
      adLoading: false,
    });

    (
      BusinessInteractionsHooks.useBusinessInteractions as jest.Mock
    ).mockReturnValue({
      interactionStatus: { isLiked: false, isSubscribed: false },
      isOwner: false,
      likeLoading: false,
      subscribeLoading: false,
      handleLikePress: mockHandleLikePress,
      handleSubscribePress: mockHandleSubscribePress,
      handleReviewPress: mockHandleReviewPress,
    });

    (LocationContext.useLocation as jest.Mock).mockReturnValue({
      currentLocation: { latitude: 12.345, longitude: 56.789 },
    });

    jest.spyOn(Alert, "alert").mockImplementation((title, message, buttons) => {
      if (buttons && buttons[1] && buttons[1].onPress) {
        buttons[1].onPress();
      }
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("renders correctly and displays the products tab by default", () => {
    const { getByText } = renderWithProviders(
      <PublicCardView businessData={mockBusinessData} />
    );

    expect(getByText("Test Cafe")).toBeTruthy();
    expect(getByText("Products")).toBeTruthy();
    expect(getByText("Coffee")).toBeTruthy();
  });

  it("switches between tabs and displays correct content", () => {
    const { getByText, queryByText } = renderWithProviders(
      <PublicCardView businessData={mockBusinessData} />
    );

    act(() => {
      fireEvent.press(getByText("About"));
    });
    expect(getByText("About This Business")).toBeTruthy();
    expect(queryByText("Coffee")).toBeNull();

    act(() => {
      fireEvent.press(getByText("Gallery"));
    });
    // Assuming GalleryTab renders something specific, e.g., an image with a testID
    // For now, just check if the tab switch happens
    expect(queryByText("About This Business")).toBeNull();

    act(() => {
      fireEvent.press(getByText("Reviews"));
    });
    expect(getByText("Great!")).toBeTruthy();
  });

  it("attempts to open maps for directions", () => {
    const { getByTestId } = renderWithProviders(
      <PublicCardView businessData={mockBusinessData} />
    );
    // Assuming BusinessStats has a button with this testID
    // fireEvent.press(getByTestId('directions-button'));
    // expect(Alert.alert).toHaveBeenCalled();
    // expect(Linking.openURL).toHaveBeenCalledWith(expect.stringContaining('https://www.google.com/maps/dir/'));
  });
});
