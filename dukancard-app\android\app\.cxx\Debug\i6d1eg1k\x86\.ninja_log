# ninja log v5
4	53	0	C:/web-app/dukancard-app/android/app/.cxx/Debug/i6d1eg1k/x86/CMakeFiles/cmake.verify_globs	6679bdb26103c078
17	16666	7736088382532573	CMakeFiles/appmodules.dir/OnLoad.cpp.o	8d62c7310c20627a
115695	137684	7736089592128082	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o	2da02826725a20be
39	11156	7736088328606402	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o	ac03aad6e3f995e8
32	14688	7736088362831357	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o	7e0dbd848739582f
25	12957	7736088345144950	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o	1f23948d4808de97
13467	28123	7736088497964204	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o	573d18799fc48553
103922	116713	7736089382994475	RNCTabView_autolinked_build/CMakeFiles/react_codegen_RNCTabView.dir/RNCTabView-generated.cpp.o	580a9fc32d832b2d
11178	26637	7736088482775058	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o	b0c541374620ed56
47	13465	7736088351812334	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o	fd02eae96751e5f9
12958	23539	7736088451950689	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o	5b5a289651e51e8d
159447	176865	7736089984995010	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/338c260dbc7daf5ba1393daac299e36a/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o	12c532e6c1504714
124221	139652	7736089611742802	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o	e631d9a4034cba51
14689	30082	7736088516593916	RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/RNGoogleSignInCGen-generated.cpp.o	57ab471148b0172c
102723	124220	7736089455274831	RNCTabView_autolinked_build/CMakeFiles/react_codegen_RNCTabView.dir/react/renderer/components/RNCTabView/Props.cpp.o	2c29e1ff988faa22
16667	36508	7736088581043232	RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/ComponentDescriptors.cpp.o	3ae5677dae9f113
172518	190699	7736090123492940	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o	2a15521de8ce656d
36509	51062	7736088726809925	RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/RNGoogleSignInCGenJSI-generated.cpp.o	dd759184d6931c59
244273	261695	7736090833013068	RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/EventEmitters.cpp.o	1b01df5840d6d00d
30083	40796	7736088623011079	RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/States.cpp.o	ed2527dfcdd2cd27
26639	39547	7736088612196642	RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/EventEmitters.cpp.o	ac5dc46e6dffc503
41216	64228	7736088857626088	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDialogPickerMeasurementsManager.cpp.o	8c0c72f778f5e231
23540	41215	7736088627807789	RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/Props.cpp.o	c7403797c5d6c61e
28177	43682	7736088652997358	RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/ShadowNodes.cpp.o	336187333fa3f682
43682	57852	7736088794763741	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDialogPickerState.cpp.o	cf90f45c16924e25
39548	61880	7736088835624329	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDropdownPickerMeasurementsManager.cpp.o	da37d3226ef3b845
165171	167735	7736089886506450	C:/web-app/dukancard-app/android/app/build/intermediates/cxx/Debug/i6d1eg1k/obj/x86/libreact_codegen_safeareacontext.so	69f0122e659ced38
9	91808	7736089127526672	CMakeFiles/appmodules.dir/C_/web-app/dukancard-app/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o	b34ca08e6f3dfc
40797	61965	7736088835845336	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDialogPickerShadowNode.cpp.o	a514dcdeeafb70f1
51064	67750	7736088893572107	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/3ec7548481bc610082b4de8f5f43df7e/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp.o	6ba66e76d66d1cdb
115756	131008	7736089526561104	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/rngesturehandler_codegenJSI-generated.cpp.o	31e5d6beb04f1e3e
246172	259100	7736090807237174	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/RNEdgeToEdgeJSI-generated.cpp.o	591f5e858f7fba82
57853	81624	7736089031383724	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDropdownPickerShadowNode.cpp.o	dd0473a3c094b91b
150066	165169	7736089868288835	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/f3b3b936946d44e4910ed7fa0cf394b7/android/build/generated/source/codegen/jni/safeareacontext-generated.cpp.o	8de74df4306e26bf
62039	78238	7736088997723793	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDropdownPickerState.cpp.o	db02d15479a00932
103180	105863	7736089269572728	C:/web-app/dukancard-app/android/app/build/intermediates/cxx/Debug/i6d1eg1k/obj/x86/libreact_codegen_rnpicker.so	8e8d1a4d642c915e
190700	204841	7736090264265444	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/65410d63313b55d86ad95bf75997a892/source/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o	ba2a005cd2361a08
91980	103921	7736089255924062	RNCTabView_autolinked_build/CMakeFiles/react_codegen_RNCTabView.dir/react/renderer/components/RNCTabView/RNCTabViewJSI-generated.cpp.o	d60f534404726977
61881	85745	7736089073176251	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/9ad54ce2cfb742c28d5cc03229fe4182/generated/source/codegen/jni/react/renderer/components/rnpicker/ComponentDescriptors.cpp.o	9393f73068178408
64229	88307	7736089098174002	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/rnpicker.cpp.o	2990fba818b36538
67751	90739	7736089123814445	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/82d0e0fdbbc6778b177f7ba1797e5335/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/Props.cpp.o	90ad06acb76bac68
78239	91979	7736089136422692	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/f40e52837148a19fc7efeeec9a7d9841/source/codegen/jni/react/renderer/components/rnpicker/rnpickerJSI-generated.cpp.o	df001c26da734c7f
81625	92875	7736089144644609	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/82d0e0fdbbc6778b177f7ba1797e5335/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/States.cpp.o	189f424f2fbb8cf7
141406	155405	7736089770316601	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/21cb4f1415440831b026fa7714b9ddbf/react/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp.o	f481b2577760680
92876	102722	7736089244177691	RNCTabView_autolinked_build/CMakeFiles/react_codegen_RNCTabView.dir/react/renderer/components/RNCTabView/States.cpp.o	712a254030c3a298
85746	103179	7736089248686295	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/3ec7548481bc610082b4de8f5f43df7e/build/generated/source/codegen/jni/react/renderer/components/rnpicker/ShadowNodes.cpp.o	43b72127984132ce
167736	182022	7736090037226974	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/795bb950a5989eaaf9f017c02be2f6c9/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp.o	1a0d27f99e91d254
91810	104809	7736089265024527	RNCTabView_autolinked_build/CMakeFiles/react_codegen_RNCTabView.dir/react/renderer/components/RNCTabView/EventEmitters.cpp.o	9ec97ec0b8cf8b36
90740	107033	7736089287027601	RNCTabView_autolinked_build/CMakeFiles/react_codegen_RNCTabView.dir/react/renderer/components/RNCTabView/ShadowNodes.cpp.o	dc9ff691a499dead
155406	172844	7736089944320738	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/338c260dbc7daf5ba1393daac299e36a/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o	dfb294830869ffeb
1	68	0	clean	5f858fbb99529edc
156373	170248	7736089918448701	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/b794cac218ed69b127afbea32bb4b210/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o	daff867c410f18d4
225776	246171	7736090677744002	RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ShadowNodes.cpp.o	6b7db2a133a41034
88308	109805	7736089314126591	RNCTabView_autolinked_build/CMakeFiles/react_codegen_RNCTabView.dir/react/renderer/components/RNCTabView/ComponentDescriptors.cpp.o	77ddc6f87059c6f
105863	115694	7736089373601626	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/States.cpp.o	8ee74f32ffb2c155
104810	115754	7736089373772488	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o	c50888c391c02d19
109806	124976	7736089466274889	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp.o	df48346e80868900
107034	126611	7736089481973850	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o	6b5df456dd38ecef
116714	136962	7736089585736635	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/36333efb3758dc8c7766b52178291aff/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o	890907edf0990b3b
131010	141405	7736089631103889	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/46366c8a3bfe46fa7e098a174dcb3581/source/codegen/jni/react/renderer/components/safeareacontext/States.cpp.o	19f01a73352ca3c6
126612	142463	7736089641830617	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/d9acfe0935d9f926fc1de2ee9f8669d8/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp.o	753ae7ab2694efe5
124977	146376	7736089680183601	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/1340f96a0a84dcce0dbb22d9ca71b7d6/jni/react/renderer/components/safeareacontext/ComponentDescriptors.cpp.o	45ed0cf3b2d6010
163955	177894	7736089993529708	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/b794cac218ed69b127afbea32bb4b210/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o	a46b2d15edd84a8c
136963	150065	7736089717103027	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/993815bb9a465e0096756a6f406f9aca/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp.o	a7d690fd31f231fb
137685	155123	7736089767761566	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/46366c8a3bfe46fa7e098a174dcb3581/source/codegen/jni/react/renderer/components/safeareacontext/Props.cpp.o	9ed5b67d72cee1dc
139653	156372	7736089779855246	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/993815bb9a465e0096756a6f406f9aca/codegen/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp.o	761ab907f6347cb9
142464	159445	7736089811348508	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/b794cac218ed69b127afbea32bb4b210/common/cpp/react/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o	e742345421b9c760
250755	261813	7736090835524950	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/EventEmitters.cpp.o	ef91ab6cefb12617
146377	163954	7736089855858998	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/b794cac218ed69b127afbea32bb4b210/common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp.o	43b62588fc96aaa5
155124	172517	7736089941739767	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/b794cac218ed69b127afbea32bb4b210/common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o	a6ab7c5b695b2cca
182023	191753	7736090134173974	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/1531eaea57de0e0b7e6f08054e1025b7/build/generated/source/codegen/jni/react/renderer/components/rnscreens/States.cpp.o	c4dea43045cddd97
172845	193536	7736090150857263	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/1531eaea57de0e0b7e6f08054e1025b7/build/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp.o	456030a92f490b28
177895	194997	7736090166010183	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/1531eaea57de0e0b7e6f08054e1025b7/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o	dae88b36567fb136
176866	200749	7736090224113312	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/2e3260238a33117ac1970c352d613a46/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp.o	3e0a7fb33e003d60
170249	201138	7736090226472001	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/65410d63313b55d86ad95bf75997a892/source/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp.o	6bcbef4f4da1cd6
193537	205511	7736090271976782	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/C_/web-app/dukancard-app/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGImageState.cpp.o	7b57370969e16861
204842	207121	7736090282141607	C:/web-app/dukancard-app/android/app/build/intermediates/cxx/Debug/i6d1eg1k/obj/x86/libreact_codegen_rnscreens.so	ce894f8e8d1e43bf
191754	211053	7736090326289231	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/C_/web-app/dukancard-app/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGImageShadowNode.cpp.o	de03d5dade2c3479
200750	214128	7736090357934587	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/C_/web-app/dukancard-app/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGLayoutableShadowNode.cpp.o	d9fa5d1587dc72f7
195000	215264	7736090368218083	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/rnsvg.cpp.o	7c464cd5daae95f
207122	219062	7736090407289025	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/144b43cb78c34782fe09b4d2b440768b/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp.o	d7b568cc3333b58e
201139	225775	7736090468915569	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/e8f14cb3b97a36338467b80bba742990/build/generated/source/codegen/jni/react/renderer/components/rnsvg/ComponentDescriptors.cpp.o	40ff290888bd0f53
211054	231242	7736090528667272	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/C_/web-app/dukancard-app/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGShadowNodes.cpp.o	99d436e3cfc06d52
205512	232767	7736090543720606	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/144b43cb78c34782fe09b4d2b440768b/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/Props.cpp.o	9a5897e4f9fa7f96
219063	233288	7736090548592242	RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/States.cpp.o	d551c55082abde0b
214129	234918	7736090564987435	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/144b43cb78c34782fe09b4d2b440768b/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/ShadowNodes.cpp.o	a7df23d56d056087
215266	239728	7736090613263105	RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/Props.cpp.o	ba35964f6641b954
232768	244272	7736090658689024	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/144b43cb78c34782fe09b4d2b440768b/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/States.cpp.o	5c10bdc51d6b013f
231243	246133	7736090678200463	RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/RNCWebViewSpecJSI-generated.cpp.o	6fd52dd2c16b7073
233289	248951	7736090705535124	RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/RNCWebViewSpec-generated.cpp.o	97b9735237ae4047
234918	249762	7736090713783154	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/e8f14cb3b97a36338467b80bba742990/build/generated/source/codegen/jni/react/renderer/components/rnsvg/rnsvgJSI-generated.cpp.o	3f772d7a3256596
249763	250754	7736090720723632	C:/web-app/dukancard-app/android/app/build/intermediates/cxx/Debug/i6d1eg1k/obj/x86/libreact_codegen_rnsvg.so	214f6f87bfc2e395
246134	260502	7736090821559556	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ComponentDescriptors.cpp.o	dd45b52e1fbe34bd
239729	260815	7736090824440030	RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ComponentDescriptors.cpp.o	acc3cc6472d1bb33
248952	262878	7736090846015880	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/RNEdgeToEdge-generated.cpp.o	3f7dd2a982f5ccea
260817	267181	7736090888826026	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/States.cpp.o	9aeb9748d4a51e5b
259103	268384	7736090901021007	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ShadowNodes.cpp.o	cee8a4fb657fbba1
260502	268661	7736090904123414	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/Props.cpp.o	476b06b552ad0cea
268662	269351	7736090909213193	C:/web-app/dukancard-app/android/app/build/intermediates/cxx/Debug/i6d1eg1k/obj/x86/libappmodules.so	2c987aadaa33c083
3	1103	0	C:/web-app/dukancard-app/android/app/.cxx/Debug/i6d1eg1k/x86/CMakeFiles/cmake.verify_globs	6679bdb26103c078
483	16285	7736124229009836	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o	5b5a289651e51e8d
598	18389	7736124250444869	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o	ac03aad6e3f995e8
395	20475	7736124271204871	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o	1f23948d4808de97
295	22199	7736124288404881	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o	fd02eae96751e5f9
182	23993	7736124306674860	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o	7e0dbd848739582f
120	26619	7736124332893250	CMakeFiles/appmodules.dir/OnLoad.cpp.o	8d62c7310c20627a
16286	34880	7736124415542559	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o	573d18799fc48553
18390	37605	7736124442602529	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o	b0c541374620ed56
23994	38200	7736124448362553	RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/EventEmitters.cpp.o	ac5dc46e6dffc503
22200	39488	7736124461852579	RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/RNGoogleSignInCGen-generated.cpp.o	57ab471148b0172c
20476	40289	7736124468772537	RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/Props.cpp.o	c7403797c5d6c61e
26620	46219	7736124527414609	RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/ComponentDescriptors.cpp.o	3ae5677dae9f113
37605	47899	7736124545894614	RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/States.cpp.o	ed2527dfcdd2cd27
34881	48801	7736124553294827	RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/RNGoogleSignInCGenJSI-generated.cpp.o	dd759184d6931c59
38201	52937	7736124596457185	RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/ShadowNodes.cpp.o	336187333fa3f682
40290	59833	7736124664499270	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDialogPickerShadowNode.cpp.o	a514dcdeeafb70f1
46220	60486	7736124671899022	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDropdownPickerState.cpp.o	db02d15479a00932
39489	60649	7736124671569016	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDialogPickerMeasurementsManager.cpp.o	8c0c72f778f5e231
48802	62735	7736124694015771	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDialogPickerState.cpp.o	cf90f45c16924e25
47900	72472	7736124789431053	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/rnpicker.cpp.o	2990fba818b36538
52938	76204	7736124822201038	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDropdownPickerMeasurementsManager.cpp.o	da37d3226ef3b845
60649	76969	7736124835421021	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/3ec7548481bc610082b4de8f5f43df7e/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp.o	6ba66e76d66d1cdb
60487	82215	7736124888955248	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDropdownPickerShadowNode.cpp.o	dd0473a3c094b91b
59834	83109	7736124898155280	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/9ad54ce2cfb742c28d5cc03229fe4182/generated/source/codegen/jni/react/renderer/components/rnpicker/ComponentDescriptors.cpp.o	9393f73068178408
62736	85096	7736124916737545	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/82d0e0fdbbc6778b177f7ba1797e5335/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/Props.cpp.o	90ad06acb76bac68
72473	85314	7736124920087655	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/f40e52837148a19fc7efeeec9a7d9841/source/codegen/jni/react/renderer/components/rnpicker/rnpickerJSI-generated.cpp.o	df001c26da734c7f
76204	85831	7736124923778070	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/82d0e0fdbbc6778b177f7ba1797e5335/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/States.cpp.o	189f424f2fbb8cf7
76970	92182	7736124989118064	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/3ec7548481bc610082b4de8f5f43df7e/build/generated/source/codegen/jni/react/renderer/components/rnpicker/ShadowNodes.cpp.o	43b72127984132ce
82216	93036	7736124997418043	RNCTabView_autolinked_build/CMakeFiles/react_codegen_RNCTabView.dir/react/renderer/components/RNCTabView/EventEmitters.cpp.o	9ec97ec0b8cf8b36
85315	93522	7736125002438075	RNCTabView_autolinked_build/CMakeFiles/react_codegen_RNCTabView.dir/react/renderer/components/RNCTabView/States.cpp.o	712a254030c3a298
92183	94663	7736125009518063	C:/web-app/dukancard-app/android/app/build/intermediates/cxx/Debug/i6d1eg1k/obj/x86/libreact_codegen_rnpicker.so	8e8d1a4d642c915e
85832	99716	7736125064048061	RNCTabView_autolinked_build/CMakeFiles/react_codegen_RNCTabView.dir/react/renderer/components/RNCTabView/ShadowNodes.cpp.o	dc9ff691a499dead
83110	100726	7736125073505280	RNCTabView_autolinked_build/CMakeFiles/react_codegen_RNCTabView.dir/react/renderer/components/RNCTabView/Props.cpp.o	2c29e1ff988faa22
85097	103224	7736125097856027	RNCTabView_autolinked_build/CMakeFiles/react_codegen_RNCTabView.dir/react/renderer/components/RNCTabView/ComponentDescriptors.cpp.o	77ddc6f87059c6f
93523	104086	7736125107776053	RNCTabView_autolinked_build/CMakeFiles/react_codegen_RNCTabView.dir/react/renderer/components/RNCTabView/RNCTabViewJSI-generated.cpp.o	d60f534404726977
93037	104411	7736125110756012	RNCTabView_autolinked_build/CMakeFiles/react_codegen_RNCTabView.dir/RNCTabView-generated.cpp.o	580a9fc32d832b2d
94663	107995	7736125146566055	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp.o	df48346e80868900
100726	113862	7736125205876022	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/rngesturehandler_codegenJSI-generated.cpp.o	31e5d6beb04f1e3e
104087	114229	7736125208936033	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/States.cpp.o	8ee74f32ffb2c155
104412	116011	7736125226977127	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o	c50888c391c02d19
99717	117121	7736125237727117	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o	6b5df456dd38ecef
103317	117543	7736125242757144	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o	e631d9a4034cba51
113863	124865	7736125314210068	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/21cb4f1415440831b026fa7714b9ddbf/react/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp.o	f481b2577760680
107996	125926	7736125325508029	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o	2da02826725a20be
114230	128104	7736125347868120	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/b794cac218ed69b127afbea32bb4b210/common/cpp/react/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o	e742345421b9c760
116012	128181	7736125347738135	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/f3b3b936946d44e4910ed7fa0cf394b7/android/build/generated/source/codegen/jni/safeareacontext-generated.cpp.o	8de74df4306e26bf
117122	134227	7736125408703791	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/36333efb3758dc8c7766b52178291aff/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o	890907edf0990b3b
117543	135648	7736125423434410	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/1340f96a0a84dcce0dbb22d9ca71b7d6/jni/react/renderer/components/safeareacontext/ComponentDescriptors.cpp.o	45ed0cf3b2d6010
124866	135827	7736125425384456	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/993815bb9a465e0096756a6f406f9aca/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp.o	a7d690fd31f231fb
128182	137887	7736125444044430	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/46366c8a3bfe46fa7e098a174dcb3581/source/codegen/jni/react/renderer/components/safeareacontext/States.cpp.o	19f01a73352ca3c6
125927	141066	7736125477244727	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/46366c8a3bfe46fa7e098a174dcb3581/source/codegen/jni/react/renderer/components/safeareacontext/Props.cpp.o	9ed5b67d72cee1dc
128104	141768	7736125484544728	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/d9acfe0935d9f926fc1de2ee9f8669d8/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp.o	753ae7ab2694efe5
137888	145663	7736125523921091	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/1531eaea57de0e0b7e6f08054e1025b7/build/generated/source/codegen/jni/react/renderer/components/rnscreens/States.cpp.o	c4dea43045cddd97
135649	145909	7736125526411102	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/65410d63313b55d86ad95bf75997a892/source/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o	ba2a005cd2361a08
134228	146826	7736125535301331	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/993815bb9a465e0096756a6f406f9aca/codegen/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp.o	761ab907f6347cb9
146827	148849	7736125551471088	C:/web-app/dukancard-app/android/app/build/intermediates/cxx/Debug/i6d1eg1k/obj/x86/libreact_codegen_safeareacontext.so	69f0122e659ced38
141067	153477	7736125601961118	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/1531eaea57de0e0b7e6f08054e1025b7/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o	dae88b36567fb136
135828	153819	7736125604761122	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/2e3260238a33117ac1970c352d613a46/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp.o	3e0a7fb33e003d60
141769	157036	7736125636811117	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/1531eaea57de0e0b7e6f08054e1025b7/build/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp.o	456030a92f490b28
148849	159481	7736125661921126	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/b794cac218ed69b127afbea32bb4b210/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o	a46b2d15edd84a8c
145664	159585	7736125662971109	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o	2a15521de8ce656d
153478	166871	7736125735393760	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/b794cac218ed69b127afbea32bb4b210/common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o	a6ab7c5b695b2cca
153821	167037	7736125737213766	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/338c260dbc7daf5ba1393daac299e36a/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o	12c532e6c1504714
145910	168430	7736125750683771	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/65410d63313b55d86ad95bf75997a892/source/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp.o	6bcbef4f4da1cd6
157037	169967	7736125766723742	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/338c260dbc7daf5ba1393daac299e36a/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o	dfb294830869ffeb
159481	171844	7736125785043732	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/b794cac218ed69b127afbea32bb4b210/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o	daff867c410f18d4
159586	174205	7736125808913765	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/b794cac218ed69b127afbea32bb4b210/common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp.o	43b62588fc96aaa5
166872	177332	7736125840509033	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/795bb950a5989eaaf9f017c02be2f6c9/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp.o	1a0d27f99e91d254
168431	177986	7736125846529028	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/144b43cb78c34782fe09b4d2b440768b/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp.o	d7b568cc3333b58e
171845	179810	7736125865049027	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/144b43cb78c34782fe09b4d2b440768b/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/States.cpp.o	5c10bdc51d6b013f
174206	185145	7736125918288696	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/e8f14cb3b97a36338467b80bba742990/build/generated/source/codegen/jni/react/renderer/components/rnsvg/rnsvgJSI-generated.cpp.o	3f772d7a3256596
185149	187516	7736125937953557	C:/web-app/dukancard-app/android/app/build/intermediates/cxx/Debug/i6d1eg1k/obj/x86/libreact_codegen_rnscreens.so	ce894f8e8d1e43bf
169968	189249	7736125956943550	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/144b43cb78c34782fe09b4d2b440768b/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/Props.cpp.o	9a5897e4f9fa7f96
177987	192252	7736125988341238	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/144b43cb78c34782fe09b4d2b440768b/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/ShadowNodes.cpp.o	a7df23d56d056087
179811	196451	7736126030961232	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/C_/web-app/dukancard-app/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGImageShadowNode.cpp.o	de03d5dade2c3479
177333	197873	7736126044941257	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/e8f14cb3b97a36338467b80bba742990/build/generated/source/codegen/jni/react/renderer/components/rnsvg/ComponentDescriptors.cpp.o	40ff290888bd0f53
187517	198950	7736126056201251	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/C_/web-app/dukancard-app/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGImageState.cpp.o	7b57370969e16861
189254	203402	7736126100681258	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/C_/web-app/dukancard-app/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGLayoutableShadowNode.cpp.o	d9fa5d1587dc72f7
196452	212787	7736126195062568	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/C_/web-app/dukancard-app/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGShadowNodes.cpp.o	99d436e3cfc06d52
192253	213162	7736126197332583	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/rnsvg.cpp.o	7c464cd5daae95f
198951	213576	7736126200762563	RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/RNCWebViewSpec-generated.cpp.o	97b9735237ae4047
213162	216674	7736126225262587	C:/web-app/dukancard-app/android/app/build/intermediates/cxx/Debug/i6d1eg1k/obj/x86/libreact_codegen_rnsvg.so	214f6f87bfc2e395
197874	219921	7736126265882545	RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ComponentDescriptors.cpp.o	acc3cc6472d1bb33
203403	221814	7736126285042810	RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/EventEmitters.cpp.o	1b01df5840d6d00d
213577	229004	7736126356882551	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/RNEdgeToEdge-generated.cpp.o	3f7dd2a982f5ccea
212788	234016	7736126407212538	RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/Props.cpp.o	ba35964f6641b954
216675	234459	7736126411692688	RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ShadowNodes.cpp.o	6b7db2a133a41034
219922	235645	7736126423667482	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ComponentDescriptors.cpp.o	dd45b52e1fbe34bd
221815	235857	7736126425217473	RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/RNCWebViewSpecJSI-generated.cpp.o	6fd52dd2c16b7073
229005	239368	7736126459697473	RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/States.cpp.o	d551c55082abde0b
234016	243947	7736126506665108	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/EventEmitters.cpp.o	ef91ab6cefb12617
167038	245990	7736126521855106	CMakeFiles/appmodules.dir/C_/web-app/dukancard-app/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o	b34ca08e6f3dfc
234557	246308	7736126530284835	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/Props.cpp.o	476b06b552ad0cea
235646	246787	7736126535234669	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/RNEdgeToEdgeJSI-generated.cpp.o	591f5e858f7fba82
235857	246985	7736126537464660	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ShadowNodes.cpp.o	cee8a4fb657fbba1
239369	247083	7736126538534661	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/States.cpp.o	9aeb9748d4a51e5b
247084	247745	7736126544124661	C:/web-app/dukancard-app/android/app/build/intermediates/cxx/Debug/i6d1eg1k/obj/x86/libappmodules.so	2c987aadaa33c083
