import React from "react";
import { fireEvent, render } from "@testing-library/react-native";
import { renderWithProviders } from "../../../__tests__/utils/testUtils";
import { Input } from "./Input";
import { Text } from "react-native";

describe("Input Component", () => {
  it("renders correctly with a label and placeholder", () => {
    const { getByText, getByPlaceholderText } = renderWithProviders(
      <Input label="Email" placeholder="Enter your email" />
    );
    expect(getByText("Email")).toBeTruthy();
    expect(getByPlaceholderText("Enter your email")).toBeTruthy();
  });

  it("displays an error message when error prop is provided", () => {
    const { getByText } = renderWithProviders(
      <Input label="Email" error="Invalid email" />
    );
    expect(getByText("Invalid email")).toBeTruthy();
  });

  it("handles onChangeText events", () => {
    const mockOnChangeText = jest.fn();
    const { getByPlaceholderText } = renderWithProviders(
      <Input
        label="Email"
        placeholder="Enter your email"
        onChangeText={mockOnChangeText}
      />
    );
    const input = getByPlaceholderText("Enter your email");
    fireEvent.changeText(input, "<EMAIL>");
    expect(mockOnChangeText).toHaveBeenCalledWith("<EMAIL>");
  });

  it("toggles password visibility", () => {
    const { getByPlaceholderText, getByTestId } = render(
      <Input type="password" placeholder="Password" />
    );
    const input = getByPlaceholderText("Password");
    const toggle = input.parent.findByProps({ name: "eye" });

    // Initially secure
    expect(input.props.secureTextEntry).toBe(true);

    // Toggle to visible
    fireEvent.press(toggle);
    expect(input.props.secureTextEntry).toBe(false);

    // Toggle back to secure
    fireEvent.press(toggle);
    expect(input.props.secureTextEntry).toBe(true);
  });

  it("renders left and right icons", () => {
    const { getByTestId } = renderWithProviders(
      <Input
        leftIcon={<Text testID="left-icon">L</Text>}
        rightIcon={<Text testID="right-icon">R</Text>}
      />
    );
    expect(getByTestId("left-icon")).toBeTruthy();
    expect(getByTestId("right-icon")).toBeTruthy();
  });

  it("displays helper text when provided", () => {
    const { getByText } = renderWithProviders(
      <Input label="Username" helperText="Only letters and numbers" />
    );
    expect(getByText("Only letters and numbers")).toBeTruthy();
  });

  it("shows an asterisk when isRequired is true", () => {
    const { getByText } = renderWithProviders(
      <Input label="Name" isRequired />
    );
    expect(getByText("*")).toBeTruthy();
  });
});
