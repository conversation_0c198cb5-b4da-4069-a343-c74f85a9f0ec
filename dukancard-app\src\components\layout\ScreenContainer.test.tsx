import React from "react";
import { Text, View } from "react-native";
import { renderWithProviders } from "../../../__tests__/utils/testUtils";
import { ScreenContainer } from "./ScreenContainer";
import { StatusBar } from "expo-status-bar";

jest.mock("react-native-safe-area-context", () => ({
  useSafeAreaInsets: () => ({ top: 50, bottom: 34, left: 0, right: 0 }),
}));

jest.mock("expo-status-bar", () => ({
  StatusBar: jest.fn(() => null),
}));

describe("ScreenContainer", () => {
  beforeEach(() => {
    (StatusBar as jest.Mock).mockClear();
  });

  it("renders children correctly", () => {
    const { getByText } = renderWithProviders(
      <ScreenContainer>
        <Text>Test Child</Text>
      </ScreenContainer>
    );
    expect(getByText("Test Child")).toBeTruthy();
  });

  it("renders a ScrollView when scrollable is true", () => {
    const { getByTestId } = renderWithProviders(
      <ScreenContainer scrollable={true} testID="container">
        <View />
      </ScreenContainer>
    );
    expect(getByTestId("container")).toBeTruthy();
  });

  it("does not render a ScrollView when scrollable is false", () => {
    const { getByTestId } = renderWithProviders(
      <ScreenContainer scrollable={false} testID="container">
        <Text>Not scrollable</Text>
      </ScreenContainer>
    );
    expect(getByTestId("container")).toBeTruthy();
  });

  it("renders KeyboardAvoidingView when showKeyboardAvoidingView is true", () => {
    const { getByTestId } = renderWithProviders(
      <ScreenContainer showKeyboardAvoidingView={true} testID="container">
        <View />
      </ScreenContainer>
    );
    expect(getByTestId("container")).toBeTruthy();
  });

  it("does not render KeyboardAvoidingView when showKeyboardAvoidingView is false", () => {
    const { getByTestId } = renderWithProviders(
      <ScreenContainer showKeyboardAvoidingView={false} testID="container">
        <View />
      </ScreenContainer>
    );
    expect(getByTestId("container")).toBeTruthy();
  });

  it("renders StatusBar when showStatusBar is true", () => {
    renderWithProviders(
      <ScreenContainer showStatusBar={true}>
        <View />
      </ScreenContainer>
    );
    expect(StatusBar).toHaveBeenCalled();
  });

  it("does not render StatusBar when showStatusBar is false", () => {
    renderWithProviders(
      <ScreenContainer showStatusBar={false}>
        <View />
      </ScreenContainer>
    );
    expect(StatusBar).not.toHaveBeenCalled();
  });

  it("applies safe area insets when includeSafeArea is true", () => {
    const { getByTestId } = renderWithProviders(
      <ScreenContainer includeSafeArea={true} testID="container">
        <View />
      </ScreenContainer>
    );
    const container = getByTestId("container");
    expect(container.props.style).toEqual(
      expect.objectContaining({
        paddingTop: 50,
        paddingBottom: 0, // default is ['top', 'left', 'right']
        paddingLeft: 0,
        paddingRight: 0,
      })
    );
  });

  it("does not apply safe area insets when includeSafeArea is false", () => {
    const { getByTestId } = renderWithProviders(
      <ScreenContainer includeSafeArea={false} testID="container">
        <View />
      </ScreenContainer>
    );
    const container = getByTestId("container");
    expect(container.props.style).toEqual(
      expect.objectContaining({
        paddingTop: 0,
        paddingBottom: 0,
        paddingLeft: 0,
        paddingRight: 0,
      })
    );
  });
});
