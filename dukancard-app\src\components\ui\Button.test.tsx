import React from "react";
import { fireEvent } from "@testing-library/react-native";
import { renderWithProviders } from "../../../__tests__/utils/testUtils";
import { Button } from "./Button";
import { Text } from "react-native";

describe("Button Component", () => {
  it("renders correctly with default props", () => {
    const { getByText } = renderWithProviders(
      <Button title="Test Button" onPress={() => {}} />
    );
    expect(getByText("Test Button")).toBeTruthy();
  });

  it("handles press events correctly", () => {
    const mockOnPress = jest.fn();
    const { getByText } = renderWithProviders(
      <Button title="Test Button" onPress={mockOnPress} />
    );

    fireEvent.press(getByText("Test Button"));
    expect(mockOnPress).toHaveBeenCalledTimes(1);
  });

  it("applies correct styling for primary variant", () => {
    const { getByText } = renderWithProviders(
      <Button title="Primary" variant="primary" onPress={() => {}} />
    );
    const button = getByText("Primary").parent;
    expect(button.props.style).toMatchObject({
      backgroundColor: "#C29D5B",
    });
  });

  it("applies correct styling for secondary variant", () => {
    const { getByText } = renderWithProviders(
      <Button title="Secondary" variant="secondary" onPress={() => {}} />
    );
    const button = getByText("Secondary").parent;
    expect(button.props.style).toMatchObject({
      backgroundColor: expect.any(String), // This can be dark or light
      borderColor: "#C29D5B",
      borderWidth: 1,
    });
  });

  it("applies correct styling for outline variant", () => {
    const { getByText } = renderWithProviders(
      <Button title="Outline" variant="outline" onPress={() => {}} />
    );
    const button = getByText("Outline").parent;
    expect(button.props.style).toMatchObject({
      backgroundColor: "transparent",
      borderColor: "#C29D5B",
      borderWidth: 1,
    });
  });

  it("handles disabled state correctly", () => {
    const mockOnPress = jest.fn();
    const { getByText } = renderWithProviders(
      <Button title="Disabled" onPress={mockOnPress} disabled />
    );

    fireEvent.press(getByText("Disabled"));
    expect(mockOnPress).not.toHaveBeenCalled();
    const button = getByText("Disabled").parent;
    expect(button.props.style).toMatchObject({ opacity: 0.6 });
  });

  it("handles loading state correctly", () => {
    const mockOnPress = jest.fn();
    const { getByText, queryByText } = renderWithProviders(
      <Button title="Loading" onPress={mockOnPress} loading />
    );

    fireEvent.press(getByText("Loading"));
    expect(mockOnPress).not.toHaveBeenCalled();
    const button = getByText("Loading").parent;
    expect(button.props.style).toMatchObject({ opacity: 0.6 });
    // Check that the activity indicator is shown
    expect(queryByText("Loading")).toBeTruthy();
  });

  it("renders an icon when provided", () => {
    const { getByTestId } = renderWithProviders(
      <Button
        title="Icon Button"
        onPress={() => {}}
        icon={<Text testID="icon">ICON</Text>}
      />
    );
    expect(getByTestId("icon")).toBeTruthy();
  });

  it("does not render an icon when loading", () => {
    const { queryByTestId } = renderWithProviders(
      <Button
        title="Icon Button"
        onPress={() => {}}
        icon={<Text testID="icon">ICON</Text>}
        loading
      />
    );
    expect(queryByTestId("icon")).toBeNull();
  });
});
