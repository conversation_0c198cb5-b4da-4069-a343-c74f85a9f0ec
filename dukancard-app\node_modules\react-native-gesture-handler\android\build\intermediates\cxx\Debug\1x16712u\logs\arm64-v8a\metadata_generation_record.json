[{"level_": 0, "message_": "Start JSON generation. Platform version: 24 min SDK version: arm64-v8a", "file_": "C:\\web-app\\dukancard-app\\node_modules\\react-native-gesture-handler\\android\\src\\main\\jni\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON 'C:\\web-app\\dukancard-app\\node_modules\\react-native-gesture-handler\\android\\.cxx\\Debug\\1x16712u\\arm64-v8a\\android_gradle_build.json' was up-to-date", "file_": "C:\\web-app\\dukancard-app\\node_modules\\react-native-gesture-handler\\android\\src\\main\\jni\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "C:\\web-app\\dukancard-app\\node_modules\\react-native-gesture-handler\\android\\src\\main\\jni\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]