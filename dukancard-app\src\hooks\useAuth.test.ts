import { renderHook, act } from "@testing-library/react-native";
import { useAuthErrorHandler } from "./useAuthErrorHandler";
import { useAuthRefresh, usePeriodicAuthValidation } from "./useAuthRefresh";
import { useAuth } from "../contexts/AuthContext";
import { useToast } from "../components/ui/Toast";
import { useNetworkStatus } from "../utils/networkStatus";
import { Alert } from "react-native";

jest.mock("../contexts/AuthContext");
jest.mock("../components/ui/Toast");
jest.mock("../utils/networkStatus");

describe("useAuthErrorHandler", () => {
  const mockUseAuth = useAuth as jest.Mock;
  const mockUseToast = useToast as jest.Mock;
  const mockUseNetworkStatus = useNetworkStatus as jest.Mock;

  beforeEach(() => {
    mockUseAuth.mockReturnValue({
      forceRefreshAuth: jest.fn(),
    });
    mockUseToast.mockReturnValue({
      error: jest.fn(),
    });
    mockUseNetworkStatus.mockReturnValue({
      isConnected: true,
    });
  });

  it("should initialize with default state", () => {
    const { result } = renderHook(() => useAuthErrorHandler());
    expect(result.current.isLoading).toBe(false);
    expect(result.current.error).toBeNull();
    expect(result.current.retryCount).toBe(0);
    expect(result.current.canRetry).toBe(true);
  });

  it("should handle successful operation", async () => {
    const { result } = renderHook(() => useAuthErrorHandler());
    const operation = jest.fn().mockResolvedValue("success");
    const onSuccess = jest.fn();

    await act(async () => {
      await result.current.executeWithErrorHandling({ operation, onSuccess });
    });

    expect(operation).toHaveBeenCalled();
    expect(onSuccess).toHaveBeenCalledWith("success");
    expect(result.current.isLoading).toBe(false);
    expect(result.current.error).toBeNull();
  });

  it("should handle failed operation and process error", async () => {
    const { result } = renderHook(() => useAuthErrorHandler());
    const error = new Error("Auth failed");
    const operation = jest.fn().mockRejectedValue(error);
    const onError = jest.fn();

    await act(async () => {
      await result.current.executeWithErrorHandling({ operation, onError });
    });

    expect(operation).toHaveBeenCalled();
    expect(onError).toHaveBeenCalled();
    expect(result.current.isLoading).toBe(false);
    expect(result.current.error).not.toBeNull();
    expect(result.current.error?.message).toBe(
      "Unable to authenticate. Please try again."
    );
  });

  it("should handle network offline error", async () => {
    mockUseNetworkStatus.mockReturnValue({ isConnected: false });
    const { result } = renderHook(() => useAuthErrorHandler());
    const operation = jest.fn();

    await act(async () => {
      await result.current.executeWithErrorHandling({ operation });
    });

    expect(operation).not.toHaveBeenCalled();
    expect(result.current.error).not.toBeNull();
    expect(result.current.error?.code).toBe("NETWORK_OFFLINE");
  });

  it("should show toast on error if enabled", async () => {
    const { result } = renderHook(() =>
      useAuthErrorHandler({ showToastOnError: true })
    );
    const error = new Error("test error");
    const operation = jest.fn().mockRejectedValue(error);

    await act(async () => {
      await result.current.executeWithErrorHandling({ operation });
    });

    expect(mockUseToast().error).toHaveBeenCalled();
  });

  it("should show alert on error if enabled", async () => {
    const { result } = renderHook(() =>
      useAuthErrorHandler({ showAlertOnError: true })
    );
    const error = new Error("test error");
    const operation = jest.fn().mockRejectedValue(error);

    await act(async () => {
      await result.current.executeWithErrorHandling({ operation });
    });

    expect(Alert.alert).toHaveBeenCalled();
  });
});

describe("useAuthRefresh", () => {
  const mockUseAuth = useAuth as jest.Mock;

  it("should refresh auth successfully", async () => {
    const forceRefreshAuth = jest.fn().mockResolvedValue(undefined);
    mockUseAuth.mockReturnValue({ forceRefreshAuth });
    const { result } = renderHook(() => useAuthRefresh());

    let refreshResult;
    await act(async () => {
      refreshResult = await result.current.refreshAuth();
    });

    expect(forceRefreshAuth).toHaveBeenCalled();
    expect(refreshResult).toEqual({ success: true });
    expect(result.current.isRefreshing).toBe(false);
  });

  it("should handle refresh auth error", async () => {
    const error = new Error("Refresh failed");
    const forceRefreshAuth = jest.fn().mockRejectedValue(error);
    mockUseAuth.mockReturnValue({ forceRefreshAuth });
    const { result } = renderHook(() => useAuthRefresh());

    let refreshResult;
    await act(async () => {
      refreshResult = await result.current.refreshAuth();
    });

    expect(forceRefreshAuth).toHaveBeenCalled();
    expect(refreshResult).toEqual({ success: false, error: "Refresh failed" });
    expect(result.current.isRefreshing).toBe(false);
  });
});

describe("usePeriodicAuthValidation", () => {
  const mockUseAuth = useAuth as jest.Mock;

  beforeEach(() => {
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  it("should perform initial validation on mount", async () => {
    const forceRefreshAuth = jest.fn().mockResolvedValue(undefined);
    mockUseAuth.mockReturnValue({ forceRefreshAuth });

    renderHook(() => usePeriodicAuthValidation());

    await act(async () => {
      jest.runOnlyPendingTimers();
    });

    expect(forceRefreshAuth).toHaveBeenCalledTimes(1);
  });

  it("should perform periodic validation", async () => {
    const forceRefreshAuth = jest.fn().mockResolvedValue(undefined);
    mockUseAuth.mockReturnValue({ forceRefreshAuth });

    renderHook(() => usePeriodicAuthValidation(1000));

    await act(async () => {
      jest.advanceTimersByTime(3000);
    });

    expect(forceRefreshAuth).toHaveBeenCalledTimes(4); // initial + 3 intervals
  });

  it("should handle validation errors", async () => {
    const error = new Error("Validation failed");
    const forceRefreshAuth = jest.fn().mockRejectedValue(error);
    mockUseAuth.mockReturnValue({ forceRefreshAuth });

    const { result } = renderHook(() => usePeriodicAuthValidation());

    await act(async () => {
      jest.runOnlyPendingTimers();
    });

    expect(result.current.validationError).toBe("Validation failed");
  });
});
