import { renderHook, act, waitFor } from "@testing-library/react-native";
import * as Location from "expo-location";
import { useLocationPermission } from "./useLocationPermission";
import { usePincodeDetails } from "./usePincodeDetails";
import * as locationService from "@/backend/supabase/services/location/locationService";
import { supabase } from "@/lib/supabase";

jest.mock("@/backend/supabase/services/location/locationService");
jest.mock("@/lib/supabase");

describe("useLocationPermission", () => {
  const mockCheckLocationPermission =
    locationService.checkLocationPermission as jest.Mock;
  const mockRequestLocationPermission =
    locationService.requestLocationPermission as jest.Mock;

  beforeEach(() => {
    jest.clearAllMocks();
    // Suppress expected console.error calls
    jest.spyOn(console, "error").mockImplementation(() => {});
  });

  afterEach(() => {
    // Restore console.error
    (console.error as jest.Mock).mockRestore();
  });

  it("should check permission on mount and set permission", async () => {
    const mockPermission = {
      status: Location.PermissionStatus.GRANTED,
      granted: true,
      canAskAgain: true,
      expires: "never",
    };
    mockCheckLocationPermission.mockResolvedValue(mockPermission);

    const { result } = renderHook(() => useLocationPermission());

    await waitFor(
      () => {
        expect(result.current.isLoading).toBe(false);
      },
      { timeout: 3000 }
    );

    expect(mockCheckLocationPermission).toHaveBeenCalled();
    expect(result.current.permission).toEqual(mockPermission);
  });

  it("should handle error when checking permission", async () => {
    mockCheckLocationPermission.mockRejectedValue(new Error("Check failed"));

    const { result } = renderHook(() => useLocationPermission());

    await waitFor(
      () => {
        expect(result.current.isLoading).toBe(false);
      },
      { timeout: 3000 }
    );

    expect(result.current.permission).toBeNull();
  });

  it("should request permission successfully", async () => {
    const mockPermission = {
      status: Location.PermissionStatus.GRANTED,
      granted: true,
      canAskAgain: true,
      expires: "never",
    };
    mockRequestLocationPermission.mockResolvedValue(mockPermission);

    const { result } = renderHook(() => useLocationPermission());

    // Wait for initial loading to complete
    await waitFor(
      () => {
        expect(result.current.isLoading).toBe(false);
      },
      { timeout: 3000 }
    );

    let permissionResult;
    await act(async () => {
      permissionResult = await result.current.requestPermission();
    });

    expect(mockRequestLocationPermission).toHaveBeenCalled();
    expect(permissionResult).toEqual(mockPermission);
    expect(result.current.permission).toEqual(mockPermission);
  });
});

describe("usePincodeDetails", () => {
  const mockSupabase = supabase as jest.Mocked<typeof supabase>;

  beforeEach(() => {
    (mockSupabase.from as jest.Mock).mockClear();
    // Suppress expected console.error calls
    jest.spyOn(console, "error").mockImplementation(() => {});
  });

  afterEach(() => {
    // Restore console.error
    (console.error as jest.Mock).mockRestore();
  });

  it("should fetch pincode details successfully", async () => {
    const mockPincodeData = {
      city: "Test City",
      state: "Test State",
      localities: ["Locality A", "Locality B"],
    };

    const mockOrder = jest.fn().mockResolvedValue({
      data: [
        {
          OfficeName: "Locality A",
          DivisionName: "Test City",
          StateName: "Test State",
        },
        {
          OfficeName: "Locality B",
          DivisionName: "Test City",
          StateName: "Test State",
        },
      ],
      error: null,
    });
    const mockEq = jest.fn().mockReturnValue({ order: mockOrder });
    const mockSelect = jest.fn().mockReturnValue({ eq: mockEq });
    (mockSupabase.from as jest.Mock).mockReturnValue({ select: mockSelect });

    const onPincodeChange = jest.fn();
    const { result } = renderHook(() => usePincodeDetails({ onPincodeChange }));

    await act(async () => {
      await result.current.handlePincodeChange("123456");
    });

    expect(result.current.isPincodeLoading).toBe(false);
    expect(result.current.availableLocalities).toEqual(
      mockPincodeData.localities
    );
    expect(onPincodeChange).toHaveBeenCalledWith(mockPincodeData);
  });

  it("should handle invalid pincode format", async () => {
    const onPincodeChange = jest.fn();
    const { result } = renderHook(() => usePincodeDetails({ onPincodeChange }));

    await act(async () => {
      await result.current.handlePincodeChange("123");
    });

    expect(result.current.isPincodeLoading).toBe(false);
    expect(result.current.availableLocalities).toEqual([]);
    expect(onPincodeChange).toHaveBeenCalledWith(null);
  });

  it("should handle pincode not found", async () => {
    const mockOrder = jest.fn().mockResolvedValue({ data: [], error: null });
    const mockEq = jest.fn().mockReturnValue({ order: mockOrder });
    const mockSelect = jest.fn().mockReturnValue({ eq: mockEq });
    (mockSupabase.from as jest.Mock).mockReturnValue({ select: mockSelect });

    const onPincodeChange = jest.fn();
    const { result } = renderHook(() => usePincodeDetails({ onPincodeChange }));

    await act(async () => {
      await result.current.handlePincodeChange("999999");
    });

    expect(result.current.isPincodeLoading).toBe(false);
    expect(result.current.availableLocalities).toEqual([]);
    expect(onPincodeChange).toHaveBeenCalledWith(null);
  });
});
