import React from "react";
import { Text, View } from "react-native";
import { renderWithProviders } from "../../../__tests__/utils/testUtils";
import { AuthContainer } from "./AuthContainer";

jest.mock("react-native-safe-area-context", () => ({
  useSafeAreaInsets: () => ({ top: 50, bottom: 34, left: 0, right: 0 }),
}));

describe("AuthContainer", () => {
  it("renders children correctly", () => {
    const { getByText } = renderWithProviders(
      <AuthContainer>
        <Text>Test Auth Child</Text>
      </AuthContainer>
    );
    expect(getByText("Test Auth Child")).toBeTruthy();
  });

  it("renders a ScrollView when scrollable is true", () => {
    const { getByTestId } = renderWithProviders(
      <AuthContainer scrollable={true} testID="auth-container">
        <View />
      </AuthContainer>
    );
    expect(getByTestId("auth-container")).toBeTruthy();
  });

  it("does not render a ScrollView when scrollable is false", () => {
    const { getByTestId } = renderWithProviders(
      <AuthContainer scrollable={false} testID="auth-container">
        <Text>Not scrollable</Text>
      </AuthContainer>
    );
    expect(getByTestId("auth-container")).toBeTruthy();
  });

  it("renders KeyboardAvoidingView when showKeyboardAvoidingView is true", () => {
    const { getByTestId } = renderWithProviders(
      <AuthContainer showKeyboardAvoidingView={true} testID="auth-container">
        <View />
      </AuthContainer>
    );
    expect(getByTestId("auth-container")).toBeTruthy();
  });

  it("does not render KeyboardAvoidingView when showKeyboardAvoidingView is false", () => {
    const { getByTestId } = renderWithProviders(
      <AuthContainer showKeyboardAvoidingView={false} testID="auth-container">
        <View />
      </AuthContainer>
    );
    expect(getByTestId("auth-container")).toBeTruthy();
  });
});
