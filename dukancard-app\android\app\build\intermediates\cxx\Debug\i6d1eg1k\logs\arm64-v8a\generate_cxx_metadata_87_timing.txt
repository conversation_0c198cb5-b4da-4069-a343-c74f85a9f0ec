# C/C++ build system timings
generate_cxx_metadata
  [gap of 224ms]
  create-invalidation-state 1094ms
  generate-prefab-packages
    [gap of 22ms]
    exec-prefab 6038ms
    [gap of 87ms]
  generate-prefab-packages completed in 6147ms
  execute-generate-process
    [gap of 51ms]
    exec-configure 4409ms
    [gap of 801ms]
  execute-generate-process completed in 5261ms
  [gap of 167ms]
  write-metadata-json-to-file 21ms
generate_cxx_metadata completed in 12934ms

