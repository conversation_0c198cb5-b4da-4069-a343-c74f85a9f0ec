{"configurations": [{"directories": [{"build": ".", "childIndexes": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.13"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}, {"build": "rnasyncstorage_autolinked_build", "jsonFile": "directory-rnasyncstorage_autolinked_build-Debug-998fc07a05a7b3bea62e.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/web-app/dukancard-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni", "targetIndexes": [5]}, {"build": "RNGoogleSignInCGen_autolinked_build", "jsonFile": "directory-RNGoogleSignInCGen_autolinked_build-Debug-2f47e063b6c5ab2f614d.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/web-app/dukancard-app/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni", "targetIndexes": [4]}, {"build": "rnpicker_autolinked_build", "jsonFile": "directory-rnpicker_autolinked_build-Debug-d46a2175a844e1b0acb8.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/web-app/dukancard-app/node_modules/@react-native-picker/picker/android/src/main/jni", "targetIndexes": [7]}, {"build": "RNCTabView_autolinked_build", "jsonFile": "directory-RNCTabView_autolinked_build-Debug-5bba9c60fdf17247a925.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/web-app/dukancard-app/node_modules/react-native-bottom-tabs/android/build/generated/source/codegen/jni", "targetIndexes": [1]}, {"build": "rngesturehandler_codegen_autolinked_build", "jsonFile": "directory-rngesturehandler_codegen_autolinked_build-Debug-004c185c6057cd395219.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/web-app/dukancard-app/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni", "targetIndexes": [6]}, {"build": "safeareacontext_autolinked_build", "jsonFile": "directory-safeareacontext_autolinked_build-Debug-42271af208264ebc01c7.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/web-app/dukancard-app/node_modules/react-native-safe-area-context/android/src/main/jni", "targetIndexes": [10]}, {"build": "rnscreens_autolinked_build", "jsonFile": "directory-rnscreens_autolinked_build-Debug-2455d5b2fd84c232e501.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/web-app/dukancard-app/node_modules/react-native-screens/android/src/main/jni", "targetIndexes": [8]}, {"build": "rnsvg_autolinked_build", "jsonFile": "directory-rnsvg_autolinked_build-Debug-4c324c4671b475c121cc.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/web-app/dukancard-app/node_modules/react-native-svg/android/src/main/jni", "targetIndexes": [9]}, {"build": "RNCWebViewSpec_autolinked_build", "jsonFile": "directory-RNCWebViewSpec_autolinked_build-Debug-e92ae4fa58e885572866.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/web-app/dukancard-app/node_modules/react-native-webview/android/build/generated/source/codegen/jni", "targetIndexes": [2]}, {"build": "RNEdgeToEdge_autolinked_build", "jsonFile": "directory-RNEdgeToEdge_autolinked_build-Debug-42ca515219a39d156c13.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/web-app/dukancard-app/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni", "targetIndexes": [3]}], "name": "Debug", "projects": [{"directoryIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "name": "appmodules", "targetIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10]}], "targets": [{"directoryIndex": 0, "id": "appmodules::@6890427a1f51a3e7e1df", "jsonFile": "target-appmodules-Debug-17414e9e38cb208ceae9.json", "name": "appmodules", "projectIndex": 0}, {"directoryIndex": 4, "id": "react_codegen_RNCTabView::@54948b52a0aeebf4e5a8", "jsonFile": "target-react_codegen_RNCTabView-Debug-b55b696fb89dcf45e662.json", "name": "react_codegen_RNCTabView", "projectIndex": 0}, {"directoryIndex": 9, "id": "react_codegen_RNCWebViewSpec::@eb48929f9f7453740a6c", "jsonFile": "target-react_codegen_RNCWebViewSpec-Debug-fc92f3ec70374a84564f.json", "name": "react_codegen_RNCWebViewSpec", "projectIndex": 0}, {"directoryIndex": 10, "id": "react_codegen_RNEdgeToEdge::@668218a48b60eb28aa9e", "jsonFile": "target-react_codegen_RNEdgeToEdge-Debug-a15135d3482a5d47a8ab.json", "name": "react_codegen_RNEdgeToEdge", "projectIndex": 0}, {"directoryIndex": 2, "id": "react_codegen_RNGoogleSignInCGen::@337b7b353bd94a4215c0", "jsonFile": "target-react_codegen_RNGoogleSignInCGen-Debug-b2f3d47b7d67f4ccc835.json", "name": "react_codegen_RNGoogleSignInCGen", "projectIndex": 0}, {"directoryIndex": 1, "id": "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe", "jsonFile": "target-react_codegen_rnasyncstorage-Debug-290c436661a79d705898.json", "name": "react_codegen_rnasyncstorage", "projectIndex": 0}, {"directoryIndex": 5, "id": "react_codegen_rngesturehandler_codegen::@39f233abcd2c728bc6ec", "jsonFile": "target-react_codegen_rngesturehandler_codegen-Debug-648af2704aa9b6dbab10.json", "name": "react_codegen_rngesturehandler_codegen", "projectIndex": 0}, {"directoryIndex": 3, "id": "react_codegen_rnpicker::@e8bb2e9e833f47d0d516", "jsonFile": "target-react_codegen_rnpicker-Debug-36ea0cebc502ed5bc0db.json", "name": "react_codegen_rnpicker", "projectIndex": 0}, {"directoryIndex": 7, "id": "react_codegen_rnscreens::@25bcbd507e98d3a854ad", "jsonFile": "target-react_codegen_rnscreens-Debug-1d0025fbdf34c673ab8e.json", "name": "react_codegen_rnscreens", "projectIndex": 0}, {"directoryIndex": 8, "id": "react_codegen_rnsvg::@4f40eb209d0c0b4a3b65", "jsonFile": "target-react_codegen_rnsvg-Debug-b41a3cddf08973dd36ba.json", "name": "react_codegen_rnsvg", "projectIndex": 0}, {"directoryIndex": 6, "id": "react_codegen_safeareacontext::@7984cd80db47aa7b952a", "jsonFile": "target-react_codegen_safeareacontext-Debug-7db7c61cebd1492b63e3.json", "name": "react_codegen_safeareacontext", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "C:/web-app/dukancard-app/android/app/.cxx/Debug/i6d1eg1k/armeabi-v7a", "source": "C:/web-app/dukancard-app/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup"}, "version": {"major": 2, "minor": 3}}