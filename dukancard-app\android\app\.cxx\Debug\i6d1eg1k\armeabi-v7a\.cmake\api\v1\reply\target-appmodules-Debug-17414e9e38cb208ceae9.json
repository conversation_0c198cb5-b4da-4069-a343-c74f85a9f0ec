{"artifacts": [{"path": "C:/web-app/dukancard-app/android/app/build/intermediates/cxx/Debug/i6d1eg1k/obj/armeabi-v7a/libappmodules.so"}], "backtrace": 3, "backtraceGraph": {"commands": ["add_library", "include", "target_link_libraries", "target_compile_options", "target_include_directories"], "files": ["C:/web-app/dukancard-app/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake", "CMakeLists.txt", "C:/web-app/dukancard-app/node_modules/@react-native-picker/picker/android/src/main/jni/CMakeLists.txt", "C:/web-app/dukancard-app/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt", "C:/web-app/dukancard-app/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt", "C:/web-app/dukancard-app/node_modules/react-native-svg/android/src/main/jni/CMakeLists.txt"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 31, "parent": 0}, {"file": 0, "parent": 1}, {"command": 0, "file": 0, "line": 56, "parent": 2}, {"command": 2, "file": 0, "line": 101, "parent": 2}, {"command": 2, "file": 0, "line": 87, "parent": 2}, {"command": 3, "file": 0, "line": 63, "parent": 2}, {"command": 4, "file": 0, "line": 58, "parent": 2}, {"file": 2}, {"command": 4, "file": 2, "line": 77, "parent": 8}, {"file": 3}, {"command": 4, "file": 3, "line": 83, "parent": 10}, {"file": 4}, {"command": 4, "file": 4, "line": 81, "parent": 12}, {"file": 5}, {"command": 4, "file": 5, "line": 88, "parent": 14}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC"}, {"backtrace": 6, "fragment": "-Wall"}, {"backtrace": 6, "fragment": "-Werror"}, {"backtrace": 6, "fragment": "-Wno-error=cpp"}, {"backtrace": 6, "fragment": "-fexceptions"}, {"backtrace": 6, "fragment": "-frtti"}, {"backtrace": 6, "fragment": "-std=c++20"}, {"backtrace": 6, "fragment": "-DLOG_TAG=\\\"ReactNative\\\""}, {"backtrace": 6, "fragment": "-DFOLLY_NO_CONFIG=1"}, {"backtrace": 4, "fragment": "-DFOLLY_HAVE_CLOCK_GETTIME=1"}, {"backtrace": 4, "fragment": "-DFOLLY_USE_LIBCPP=1"}, {"backtrace": 4, "fragment": "-DFOLLY_CFG_NO_COROUTINES=1"}, {"backtrace": 4, "fragment": "-DFOLLY_MOBILE=1"}, {"backtrace": 4, "fragment": "-DFOLLY_HAVE_RECVMMSG=1"}, {"backtrace": 4, "fragment": "-DFOLLY_HAVE_PTHREAD=1"}, {"backtrace": 4, "fragment": "-DFOLLY_HAVE_XSI_STRERROR_R=1"}], "defines": [{"define": "appmodules_EXPORTS"}], "includes": [{"backtrace": 7, "path": "C:/web-app/dukancard-app/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup"}, {"backtrace": 7, "path": "C:/web-app/dukancard-app/android/app/build/generated/autolinking/src/main/jni"}, {"backtrace": 9, "path": "C:/web-app/dukancard-app/node_modules/@react-native-picker/picker/android/src/main/jni"}, {"backtrace": 11, "path": "C:/web-app/dukancard-app/node_modules/react-native-safe-area-context/android/src/main/jni"}, {"backtrace": 13, "path": "C:/web-app/dukancard-app/node_modules/react-native-screens/android/src/main/jni"}, {"backtrace": 15, "path": "C:/web-app/dukancard-app/node_modules/react-native-svg/android/src/main/jni"}, {"backtrace": 4, "path": "C:/web-app/dukancard-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "C:/web-app/dukancard-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage"}, {"backtrace": 4, "path": "C:/web-app/dukancard-app/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "C:/web-app/dukancard-app/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleSignInCGen"}, {"backtrace": 4, "path": "C:/web-app/dukancard-app/node_modules/@react-native-picker/picker/android/src/main/jni/."}, {"backtrace": 4, "path": "C:/web-app/dukancard-app/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni"}, {"backtrace": 4, "path": "C:/web-app/dukancard-app/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker"}, {"backtrace": 4, "path": "C:/web-app/dukancard-app/node_modules/react-native-bottom-tabs/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "C:/web-app/dukancard-app/node_modules/react-native-bottom-tabs/android/build/generated/source/codegen/jni/react/renderer/components/RNCTabView"}, {"backtrace": 4, "path": "C:/web-app/dukancard-app/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "C:/web-app/dukancard-app/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen"}, {"backtrace": 4, "path": "C:/web-app/dukancard-app/node_modules/react-native-safe-area-context/android/src/main/jni/."}, {"backtrace": 4, "path": "C:/web-app/dukancard-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp"}, {"backtrace": 4, "path": "C:/web-app/dukancard-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni"}, {"backtrace": 4, "path": "C:/web-app/dukancard-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext"}, {"backtrace": 4, "path": "C:/web-app/dukancard-app/node_modules/react-native-screens/android/src/main/jni/."}, {"backtrace": 4, "path": "C:/web-app/dukancard-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp"}, {"backtrace": 4, "path": "C:/web-app/dukancard-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni"}, {"backtrace": 4, "path": "C:/web-app/dukancard-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens"}, {"backtrace": 4, "path": "C:/web-app/dukancard-app/node_modules/react-native-svg/android/src/main/jni/."}, {"backtrace": 4, "path": "C:/web-app/dukancard-app/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp"}, {"backtrace": 4, "path": "C:/web-app/dukancard-app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni"}, {"backtrace": 4, "path": "C:/web-app/dukancard-app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg"}, {"backtrace": 4, "path": "C:/web-app/dukancard-app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "C:/web-app/dukancard-app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec"}, {"backtrace": 4, "path": "C:/web-app/dukancard-app/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "C:/web-app/dukancard-app/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge"}, {"backtrace": 5, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.13/transforms/7f5330b574122aa672d96a0f5922e634/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"}, {"backtrace": 5, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.13/transforms/804afae0b0d5554ac4e7feda2f2ca6c6/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include"}, {"backtrace": 5, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.13/transforms/804afae0b0d5554ac4e7feda2f2ca6c6/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"}], "language": "CXX", "sourceIndexes": [0, 1], "sysroot": {"path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}], "dependencies": [{"backtrace": 4, "id": "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe"}, {"backtrace": 4, "id": "react_codegen_RNGoogleSignInCGen::@337b7b353bd94a4215c0"}, {"backtrace": 4, "id": "react_codegen_rnpicker::@e8bb2e9e833f47d0d516"}, {"backtrace": 4, "id": "react_codegen_RNCTabView::@54948b52a0aeebf4e5a8"}, {"backtrace": 4, "id": "react_codegen_rngesturehandler_codegen::@39f233abcd2c728bc6ec"}, {"backtrace": 4, "id": "react_codegen_safeareacontext::@7984cd80db47aa7b952a"}, {"backtrace": 4, "id": "react_codegen_rnscreens::@25bcbd507e98d3a854ad"}, {"backtrace": 4, "id": "react_codegen_RNCWebViewSpec::@eb48929f9f7453740a6c"}, {"backtrace": 4, "id": "react_codegen_rnsvg::@4f40eb209d0c0b4a3b65"}, {"backtrace": 4, "id": "react_codegen_RNEdgeToEdge::@668218a48b60eb28aa9e"}], "id": "appmodules::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments", "role": "flags"}, {"backtrace": 4, "fragment": "C:\\web-app\\dukancard-app\\android\\app\\build\\intermediates\\cxx\\Debug\\i6d1eg1k\\obj\\armeabi-v7a\\libreact_codegen_rnpicker.so", "role": "libraries"}, {"backtrace": 4, "fragment": "C:\\web-app\\dukancard-app\\android\\app\\build\\intermediates\\cxx\\Debug\\i6d1eg1k\\obj\\armeabi-v7a\\libreact_codegen_safeareacontext.so", "role": "libraries"}, {"backtrace": 4, "fragment": "C:\\web-app\\dukancard-app\\android\\app\\build\\intermediates\\cxx\\Debug\\i6d1eg1k\\obj\\armeabi-v7a\\libreact_codegen_rnscreens.so", "role": "libraries"}, {"backtrace": 4, "fragment": "C:\\web-app\\dukancard-app\\android\\app\\build\\intermediates\\cxx\\Debug\\i6d1eg1k\\obj\\armeabi-v7a\\libreact_codegen_rnsvg.so", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7f5330b574122aa672d96a0f5922e634\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.armeabi-v7a\\libfbjni.so", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\804afae0b0d5554ac4e7feda2f2ca6c6\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\jsi\\libs\\android.armeabi-v7a\\libjsi.so", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\804afae0b0d5554ac4e7feda2f2ca6c6\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\reactnative\\libs\\android.armeabi-v7a\\libreactnative.so", "role": "libraries"}, {"fragment": "-latomic -lm", "role": "libraries"}], "language": "CXX", "sysroot": {"path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}, "name": "appmodules", "nameOnDisk": "libappmodules.so", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1]}, {"name": "Object Libraries", "sourceIndexes": [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43]}], "sources": [{"backtrace": 3, "compileGroupIndex": 0, "path": "C:/web-app/dukancard-app/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "OnLoad.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "isGenerated": true, "path": "C:/web-app/dukancard-app/android/app/.cxx/Debug/i6d1eg1k/armeabi-v7a/rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/web-app/dukancard-app/android/app/.cxx/Debug/i6d1eg1k/armeabi-v7a/rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/web-app/dukancard-app/android/app/.cxx/Debug/i6d1eg1k/armeabi-v7a/rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/web-app/dukancard-app/android/app/.cxx/Debug/i6d1eg1k/armeabi-v7a/rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/web-app/dukancard-app/android/app/.cxx/Debug/i6d1eg1k/armeabi-v7a/rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/web-app/dukancard-app/android/app/.cxx/Debug/i6d1eg1k/armeabi-v7a/rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/web-app/dukancard-app/android/app/.cxx/Debug/i6d1eg1k/armeabi-v7a/rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/web-app/dukancard-app/android/app/.cxx/Debug/i6d1eg1k/armeabi-v7a/RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/RNGoogleSignInCGen-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/web-app/dukancard-app/android/app/.cxx/Debug/i6d1eg1k/armeabi-v7a/RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/web-app/dukancard-app/android/app/.cxx/Debug/i6d1eg1k/armeabi-v7a/RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/web-app/dukancard-app/android/app/.cxx/Debug/i6d1eg1k/armeabi-v7a/RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/web-app/dukancard-app/android/app/.cxx/Debug/i6d1eg1k/armeabi-v7a/RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/RNGoogleSignInCGenJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/web-app/dukancard-app/android/app/.cxx/Debug/i6d1eg1k/armeabi-v7a/RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/web-app/dukancard-app/android/app/.cxx/Debug/i6d1eg1k/armeabi-v7a/RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/States.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/web-app/dukancard-app/android/app/.cxx/Debug/i6d1eg1k/armeabi-v7a/RNCTabView_autolinked_build/CMakeFiles/react_codegen_RNCTabView.dir/RNCTabView-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/web-app/dukancard-app/android/app/.cxx/Debug/i6d1eg1k/armeabi-v7a/RNCTabView_autolinked_build/CMakeFiles/react_codegen_RNCTabView.dir/react/renderer/components/RNCTabView/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/web-app/dukancard-app/android/app/.cxx/Debug/i6d1eg1k/armeabi-v7a/RNCTabView_autolinked_build/CMakeFiles/react_codegen_RNCTabView.dir/react/renderer/components/RNCTabView/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/web-app/dukancard-app/android/app/.cxx/Debug/i6d1eg1k/armeabi-v7a/RNCTabView_autolinked_build/CMakeFiles/react_codegen_RNCTabView.dir/react/renderer/components/RNCTabView/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/web-app/dukancard-app/android/app/.cxx/Debug/i6d1eg1k/armeabi-v7a/RNCTabView_autolinked_build/CMakeFiles/react_codegen_RNCTabView.dir/react/renderer/components/RNCTabView/RNCTabViewJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/web-app/dukancard-app/android/app/.cxx/Debug/i6d1eg1k/armeabi-v7a/RNCTabView_autolinked_build/CMakeFiles/react_codegen_RNCTabView.dir/react/renderer/components/RNCTabView/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/web-app/dukancard-app/android/app/.cxx/Debug/i6d1eg1k/armeabi-v7a/RNCTabView_autolinked_build/CMakeFiles/react_codegen_RNCTabView.dir/react/renderer/components/RNCTabView/States.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/web-app/dukancard-app/android/app/.cxx/Debug/i6d1eg1k/armeabi-v7a/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/web-app/dukancard-app/android/app/.cxx/Debug/i6d1eg1k/armeabi-v7a/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/web-app/dukancard-app/android/app/.cxx/Debug/i6d1eg1k/armeabi-v7a/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/web-app/dukancard-app/android/app/.cxx/Debug/i6d1eg1k/armeabi-v7a/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/web-app/dukancard-app/android/app/.cxx/Debug/i6d1eg1k/armeabi-v7a/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/States.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/web-app/dukancard-app/android/app/.cxx/Debug/i6d1eg1k/armeabi-v7a/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/rngesturehandler_codegenJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/web-app/dukancard-app/android/app/.cxx/Debug/i6d1eg1k/armeabi-v7a/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/web-app/dukancard-app/android/app/.cxx/Debug/i6d1eg1k/armeabi-v7a/RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/RNCWebViewSpec-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/web-app/dukancard-app/android/app/.cxx/Debug/i6d1eg1k/armeabi-v7a/RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/web-app/dukancard-app/android/app/.cxx/Debug/i6d1eg1k/armeabi-v7a/RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/web-app/dukancard-app/android/app/.cxx/Debug/i6d1eg1k/armeabi-v7a/RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/web-app/dukancard-app/android/app/.cxx/Debug/i6d1eg1k/armeabi-v7a/RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/RNCWebViewSpecJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/web-app/dukancard-app/android/app/.cxx/Debug/i6d1eg1k/armeabi-v7a/RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/web-app/dukancard-app/android/app/.cxx/Debug/i6d1eg1k/armeabi-v7a/RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/States.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/web-app/dukancard-app/android/app/.cxx/Debug/i6d1eg1k/armeabi-v7a/RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/RNEdgeToEdge-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/web-app/dukancard-app/android/app/.cxx/Debug/i6d1eg1k/armeabi-v7a/RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/web-app/dukancard-app/android/app/.cxx/Debug/i6d1eg1k/armeabi-v7a/RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/web-app/dukancard-app/android/app/.cxx/Debug/i6d1eg1k/armeabi-v7a/RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/web-app/dukancard-app/android/app/.cxx/Debug/i6d1eg1k/armeabi-v7a/RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/RNEdgeToEdgeJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/web-app/dukancard-app/android/app/.cxx/Debug/i6d1eg1k/armeabi-v7a/RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/web-app/dukancard-app/android/app/.cxx/Debug/i6d1eg1k/armeabi-v7a/RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/States.cpp.o", "sourceGroupIndex": 1}], "type": "SHARED_LIBRARY"}