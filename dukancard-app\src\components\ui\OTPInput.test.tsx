import React from "react";
import { fireEvent, render } from "@testing-library/react-native";
import { renderWithProviders } from "../../../__tests__/utils/testUtils";
import { OTPInput } from "./OTPInput";

describe("OTPInput Component", () => {
  it("renders the correct number of inputs", () => {
    const { getAllByRole } = renderWithProviders(
      <OTPInput length={4} onComplete={() => {}} />
    );
    expect(getAllByRole("textinput")).toHaveLength(4);
  });

  it("calls onComplete when all digits are entered", () => {
    const mockOnComplete = jest.fn();
    const { getAllByRole } = renderWithProviders(
      <OTPInput length={4} onComplete={mockOnComplete} />
    );
    const inputs = getAllByRole("textinput");
    fireEvent.changeText(inputs[0], "1");
    fireEvent.changeText(inputs[1], "2");
    fireEvent.changeText(inputs[2], "3");
    fireEvent.changeText(inputs[3], "4");
    expect(mockOnComplete).toHaveBeenCalledWith("1234");
  });

  it("focuses the next input on digit entry", () => {
    const { getAllByRole } = renderWithProviders(
      <OTPInput length={4} onComplete={() => {}} />
    );
    const inputs = getAllByRole("textinput");
    fireEvent.changeText(inputs[0], "1");
    expect(inputs[1].props.accessibilityState.focused).toBe(true);
  });

  it("handles backspace correctly", () => {
    const { getAllByRole } = renderWithProviders(
      <OTPInput length={4} onComplete={() => {}} />
    );
    const inputs = getAllByRole("textinput");
    fireEvent.changeText(inputs[0], "1");
    fireEvent.changeText(inputs[1], "2");
    fireEvent(inputs[1], "keyPress", { nativeEvent: { key: "Backspace" } });
    expect(inputs[1].props.value).toBe("");
    expect(inputs[0].props.accessibilityState.focused).toBe(true);
  });

  it("handles pasting an OTP", () => {
    const mockOnComplete = jest.fn();
    const { getAllByRole } = renderWithProviders(
      <OTPInput length={4} onComplete={mockOnComplete} />
    );
    const inputs = getAllByRole("textinput");
    fireEvent.changeText(inputs[0], "1234");
    expect(mockOnComplete).toHaveBeenCalledWith("1234");
    expect(inputs[0].props.value).toBe("1");
    expect(inputs[1].props.value).toBe("2");
    expect(inputs[2].props.value).toBe("3");
    expect(inputs[3].props.value).toBe("4");
  });

  it("displays an error message", () => {
    const { getByText } = renderWithProviders(
      <OTPInput length={4} onComplete={() => {}} error="Invalid OTP" />
    );
    expect(getByText("Invalid OTP")).toBeTruthy();
  });

  it("is not editable when disabled", () => {
    const { getAllByRole } = renderWithProviders(
      <OTPInput length={4} onComplete={() => {}} disabled />
    );
    const inputs = getAllByRole("textinput");
    inputs.forEach((input) => {
      expect(input.props.editable).toBe(false);
    });
  });
});
