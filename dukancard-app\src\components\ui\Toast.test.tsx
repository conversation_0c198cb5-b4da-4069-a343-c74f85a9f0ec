import React from "react";
import { fireEvent, render, act } from "@testing-library/react-native";
import { ToastProvider, useToast } from "./Toast";
import { Button, Text, View } from "react-native";

// Mock timers
jest.useFakeTimers();

const TestComponent = () => {
  const toast = useToast();
  return (
    <View>
      <Button
        title="Show Success"
        onPress={() => toast.success("Success!", "This is a success message.")}
      />
      <Button
        title="Show Error"
        onPress={() => toast.error("Error!", "This is an error message.")}
      />
      <Button
        title="Show Info"
        onPress={() => toast.info("Info", "This is an info message.")}
      />
      <Button
        title="Show Warning"
        onPress={() => toast.warning("Warning", "This is a warning message.")}
      />
    </View>
  );
};

describe("Toast Component", () => {
  it("displays a success toast", async () => {
    const { getByText, queryByText } = render(
      <ToastProvider>
        <TestComponent />
      </ToastProvider>
    );

    fireEvent.press(getByText("Show Success"));

    expect(getByText("Success!")).toBeTruthy();
    expect(getByText("This is a success message.")).toBeTruthy();

    act(() => {
      jest.advanceTimersByTime(5000);
    });

    expect(queryByText("Success!")).toBeNull();
  });

  it("displays an error toast", () => {
    const { getByText } = render(
      <ToastProvider>
        <TestComponent />
      </ToastProvider>
    );

    fireEvent.press(getByText("Show Error"));

    expect(getByText("Error!")).toBeTruthy();
    expect(getByText("This is an error message.")).toBeTruthy();
  });

  it("can be dismissed by pressing on it", () => {
    const { getByText, queryByText } = render(
      <ToastProvider>
        <TestComponent />
      </ToastProvider>
    );

    fireEvent.press(getByText("Show Info"));
    const toastElement = getByText("Info");
    expect(toastElement).toBeTruthy();

    fireEvent.press(toastElement);

    act(() => {
      jest.advanceTimersByTime(500); // Allow dismiss animation to complete
    });

    expect(queryByText("Info")).toBeNull();
  });
});
