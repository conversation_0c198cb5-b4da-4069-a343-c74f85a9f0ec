export const createMockUser = (overrides = {}) => ({
  id: "test-user-id",
  email: "<EMAIL>",
  ...overrides,
});

import { BusinessProfilesRow } from "@/src/types/database";

export const createMockBusinessProfile = (
  overrides: Partial<BusinessProfilesRow> = {}
): BusinessProfilesRow => ({
  id: "test-business-id",
  business_name: "Test Business",
  contact_email: "<EMAIL>",
  has_active_subscription: false,
  trial_end_date: null,
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
  logo_url: null,
  member_name: "Test Member",
  phone: "1234567890",
  instagram_url: null,
  whatsapp_number: null,
  status: "online",
  title: "Test Title",
  address_line: "123 Test St",
  city: "Test City",
  state: "Test State",
  pincode: "12345",
  locality: "Test Locality",
  about_bio: "This is a test business.",
  facebook_url: null,
  average_rating: 4.5,
  total_likes: 100,
  total_subscriptions: 50,
  theme_color: null,
  business_hours: null,
  delivery_info: null,
  total_visits: 1000,
  today_visits: 10,
  yesterday_visits: 20,
  visits_7_days: 70,
  visits_30_days: 300,
  business_category: "Test Category",
  business_slug: "test-business",
  gallery: null,
  city_slug: "test-city",
  state_slug: "test-state",
  locality_slug: "test-locality",
  custom_branding: null,
  custom_ads: null,
  established_year: 2020,
  latitude: 12.34,
  longitude: 56.78,
  ...overrides,
});
