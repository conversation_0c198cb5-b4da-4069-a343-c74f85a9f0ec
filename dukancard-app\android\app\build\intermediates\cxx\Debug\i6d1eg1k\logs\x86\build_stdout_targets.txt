ninja: Entering directory `C:\web-app\dukancard-app\android\app\.cxx\Debug\i6d1eg1k\x86'
[0/2] Re-checking globbed directories...
[1/97] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o
[2/97] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o
[3/97] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o
[4/97] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o
[5/97] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o
[6/97] Building CXX object CMakeFiles/appmodules.dir/OnLoad.cpp.o
[7/97] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o
[8/97] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o
[9/97] Building CXX object RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/EventEmitters.cpp.o
[10/97] Building CXX object RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/RNGoogleSignInCGen-generated.cpp.o
[11/97] Building CXX object RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/Props.cpp.o
[12/97] Building CXX object RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/ComponentDescriptors.cpp.o
[13/97] Building CXX object RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/States.cpp.o
[14/97] Building CXX object RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/RNGoogleSignInCGenJSI-generated.cpp.o
[15/97] Building CXX object RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/ShadowNodes.cpp.o
[16/97] Building CXX object rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDialogPickerShadowNode.cpp.o
[17/97] Building CXX object rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDropdownPickerState.cpp.o
[18/97] Building CXX object rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDialogPickerMeasurementsManager.cpp.o
[19/97] Building CXX object rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDialogPickerState.cpp.o
[20/97] Building CXX object rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/rnpicker.cpp.o
[21/97] Building CXX object rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDropdownPickerMeasurementsManager.cpp.o
[22/97] Building CXX object rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/3ec7548481bc610082b4de8f5f43df7e/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp.o
C:/web-app/dukancard-app/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:16:60: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   16 | void RNCAndroidDialogPickerEventEmitter::onSelect(OnSelect $event) const {
      |                                                            ^
C:/web-app/dukancard-app/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:17:31: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   17 |   dispatchEvent("topSelect", [$event=std::move($event)](jsi::Runtime &runtime) {
      |                               ^
C:/web-app/dukancard-app/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:17:48: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   17 |   dispatchEvent("topSelect", [$event=std::move($event)](jsi::Runtime &runtime) {
      |                                                ^
C:/web-app/dukancard-app/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:18:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   18 |     auto $payload = jsi::Object(runtime);
      |          ^
C:/web-app/dukancard-app/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:19:5: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   19 |     $payload.setProperty(runtime, "position", $event.position);
      |     ^
C:/web-app/dukancard-app/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:19:47: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   19 |     $payload.setProperty(runtime, "position", $event.position);
      |                                               ^
C:/web-app/dukancard-app/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:20:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   20 |     return $payload;
      |            ^
C:/web-app/dukancard-app/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:25:58: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   25 | void RNCAndroidDialogPickerEventEmitter::onFocus(OnFocus $event) const {
      |                                                          ^
C:/web-app/dukancard-app/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:27:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   27 |     auto $payload = jsi::Object(runtime);
      |          ^
C:/web-app/dukancard-app/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:29:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   29 |     return $payload;
      |            ^
C:/web-app/dukancard-app/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:34:56: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   34 | void RNCAndroidDialogPickerEventEmitter::onBlur(OnBlur $event) const {
      |                                                        ^
C:/web-app/dukancard-app/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:36:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   36 |     auto $payload = jsi::Object(runtime);
      |          ^
C:/web-app/dukancard-app/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:38:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   38 |     return $payload;
      |            ^
C:/web-app/dukancard-app/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:43:62: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   43 | void RNCAndroidDropdownPickerEventEmitter::onSelect(OnSelect $event) const {
      |                                                              ^
C:/web-app/dukancard-app/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:44:31: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   44 |   dispatchEvent("topSelect", [$event=std::move($event)](jsi::Runtime &runtime) {
      |                               ^
C:/web-app/dukancard-app/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:44:48: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   44 |   dispatchEvent("topSelect", [$event=std::move($event)](jsi::Runtime &runtime) {
      |                                                ^
C:/web-app/dukancard-app/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:45:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   45 |     auto $payload = jsi::Object(runtime);
      |          ^
C:/web-app/dukancard-app/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:46:5: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   46 |     $payload.setProperty(runtime, "position", $event.position);
      |     ^
C:/web-app/dukancard-app/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:46:47: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   46 |     $payload.setProperty(runtime, "position", $event.position);
      |                                               ^
C:/web-app/dukancard-app/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:47:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   47 |     return $payload;
      |            ^
C:/web-app/dukancard-app/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:52:60: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   52 | void RNCAndroidDropdownPickerEventEmitter::onFocus(OnFocus $event) const {
      |                                                            ^
C:/web-app/dukancard-app/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:54:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   54 |     auto $payload = jsi::Object(runtime);
      |          ^
C:/web-app/dukancard-app/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:56:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   56 |     return $payload;
      |            ^
C:/web-app/dukancard-app/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:61:58: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   61 | void RNCAndroidDropdownPickerEventEmitter::onBlur(OnBlur $event) const {
      |                                                          ^
C:/web-app/dukancard-app/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:63:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   63 |     auto $payload = jsi::Object(runtime);
      |          ^
C:/web-app/dukancard-app/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:65:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   65 |     return $payload;
      |            ^
C:/web-app/dukancard-app/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:70:47: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   70 | void RNCPickerEventEmitter::onChange(OnChange $event) const {
      |                                               ^
C:/web-app/dukancard-app/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:71:28: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   71 |   dispatchEvent("change", [$event=std::move($event)](jsi::Runtime &runtime) {
      |                            ^
C:/web-app/dukancard-app/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:71:45: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   71 |   dispatchEvent("change", [$event=std::move($event)](jsi::Runtime &runtime) {
      |                                             ^
C:/web-app/dukancard-app/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:72:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   72 |     auto $payload = jsi::Object(runtime);
      |          ^
C:/web-app/dukancard-app/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:73:5: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   73 |     $payload.setProperty(runtime, "newValue", jsi::valueFromDynamic(runtime, $event.newValue));
      |     ^
C:/web-app/dukancard-app/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:73:78: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   73 |     $payload.setProperty(runtime, "newValue", jsi::valueFromDynamic(runtime, $event.newValue));
      |                                                                              ^
C:/web-app/dukancard-app/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:74:1: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   74 | $payload.setProperty(runtime, "newIndex", $event.newIndex);
      | ^
C:/web-app/dukancard-app/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:74:43: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   74 | $payload.setProperty(runtime, "newIndex", $event.newIndex);
      |                                           ^
C:/web-app/dukancard-app/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp:75:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   75 |     return $payload;
      |            ^
35 warnings generated.
[23/97] Building CXX object rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDropdownPickerShadowNode.cpp.o
[24/97] Building CXX object rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/9ad54ce2cfb742c28d5cc03229fe4182/generated/source/codegen/jni/react/renderer/components/rnpicker/ComponentDescriptors.cpp.o
[25/97] Building CXX object rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/82d0e0fdbbc6778b177f7ba1797e5335/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/Props.cpp.o
[26/97] Building CXX object rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/f40e52837148a19fc7efeeec9a7d9841/source/codegen/jni/react/renderer/components/rnpicker/rnpickerJSI-generated.cpp.o
[27/97] Building CXX object rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/82d0e0fdbbc6778b177f7ba1797e5335/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/States.cpp.o
[28/97] Building CXX object rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/3ec7548481bc610082b4de8f5f43df7e/build/generated/source/codegen/jni/react/renderer/components/rnpicker/ShadowNodes.cpp.o
[29/97] Building CXX object RNCTabView_autolinked_build/CMakeFiles/react_codegen_RNCTabView.dir/react/renderer/components/RNCTabView/EventEmitters.cpp.o
[30/97] Building CXX object RNCTabView_autolinked_build/CMakeFiles/react_codegen_RNCTabView.dir/react/renderer/components/RNCTabView/States.cpp.o
[31/97] Linking CXX shared library C:\web-app\dukancard-app\android\app\build\intermediates\cxx\Debug\i6d1eg1k\obj\x86\libreact_codegen_rnpicker.so
[32/97] Building CXX object RNCTabView_autolinked_build/CMakeFiles/react_codegen_RNCTabView.dir/react/renderer/components/RNCTabView/ShadowNodes.cpp.o
[33/97] Building CXX object RNCTabView_autolinked_build/CMakeFiles/react_codegen_RNCTabView.dir/react/renderer/components/RNCTabView/Props.cpp.o
[34/97] Building CXX object RNCTabView_autolinked_build/CMakeFiles/react_codegen_RNCTabView.dir/react/renderer/components/RNCTabView/ComponentDescriptors.cpp.o
[35/97] Building CXX object RNCTabView_autolinked_build/CMakeFiles/react_codegen_RNCTabView.dir/react/renderer/components/RNCTabView/RNCTabViewJSI-generated.cpp.o
[36/97] Building CXX object RNCTabView_autolinked_build/CMakeFiles/react_codegen_RNCTabView.dir/RNCTabView-generated.cpp.o
[37/97] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp.o
[38/97] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/rngesturehandler_codegenJSI-generated.cpp.o
[39/97] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/States.cpp.o
[40/97] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o
[41/97] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o
[42/97] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o
[43/97] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/21cb4f1415440831b026fa7714b9ddbf/react/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp.o
[44/97] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o
[45/97] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/b794cac218ed69b127afbea32bb4b210/common/cpp/react/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o
[46/97] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/f3b3b936946d44e4910ed7fa0cf394b7/android/build/generated/source/codegen/jni/safeareacontext-generated.cpp.o
[47/97] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/36333efb3758dc8c7766b52178291aff/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o
[48/97] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/1340f96a0a84dcce0dbb22d9ca71b7d6/jni/react/renderer/components/safeareacontext/ComponentDescriptors.cpp.o
[49/97] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/993815bb9a465e0096756a6f406f9aca/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp.o
[50/97] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/46366c8a3bfe46fa7e098a174dcb3581/source/codegen/jni/react/renderer/components/safeareacontext/States.cpp.o
[51/97] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/46366c8a3bfe46fa7e098a174dcb3581/source/codegen/jni/react/renderer/components/safeareacontext/Props.cpp.o
[52/97] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/d9acfe0935d9f926fc1de2ee9f8669d8/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp.o
[53/97] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/1531eaea57de0e0b7e6f08054e1025b7/build/generated/source/codegen/jni/react/renderer/components/rnscreens/States.cpp.o
[54/97] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/65410d63313b55d86ad95bf75997a892/source/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o
[55/97] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/993815bb9a465e0096756a6f406f9aca/codegen/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp.o
[56/97] Linking CXX shared library C:\web-app\dukancard-app\android\app\build\intermediates\cxx\Debug\i6d1eg1k\obj\x86\libreact_codegen_safeareacontext.so
[57/97] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/1531eaea57de0e0b7e6f08054e1025b7/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o
[58/97] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/2e3260238a33117ac1970c352d613a46/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp.o
[59/97] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/1531eaea57de0e0b7e6f08054e1025b7/build/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp.o
[60/97] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/b794cac218ed69b127afbea32bb4b210/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o
[61/97] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o
[62/97] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/b794cac218ed69b127afbea32bb4b210/common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o
[63/97] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/338c260dbc7daf5ba1393daac299e36a/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o
[64/97] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/65410d63313b55d86ad95bf75997a892/source/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp.o
[65/97] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/338c260dbc7daf5ba1393daac299e36a/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o
[66/97] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/b794cac218ed69b127afbea32bb4b210/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o
[67/97] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/b794cac218ed69b127afbea32bb4b210/common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp.o
[68/97] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/795bb950a5989eaaf9f017c02be2f6c9/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp.o
[69/97] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/144b43cb78c34782fe09b4d2b440768b/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp.o
C:/web-app/dukancard-app/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:31:44: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   31 | void RNSVGImageEventEmitter::onLoad(OnLoad $event) const {
      |                                            ^
C:/web-app/dukancard-app/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:32:26: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   32 |   dispatchEvent("load", [$event=std::move($event)](jsi::Runtime &runtime) {
      |                          ^
C:/web-app/dukancard-app/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:32:43: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   32 |   dispatchEvent("load", [$event=std::move($event)](jsi::Runtime &runtime) {
      |                                           ^
C:/web-app/dukancard-app/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:33:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   33 |     auto $payload = jsi::Object(runtime);
      |          ^
C:/web-app/dukancard-app/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:36:40: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   36 |   source.setProperty(runtime, "width", $event.source.width);
      |                                        ^
C:/web-app/dukancard-app/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:37:41: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   37 |   source.setProperty(runtime, "height", $event.source.height);
      |                                         ^
C:/web-app/dukancard-app/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:38:38: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   38 |   source.setProperty(runtime, "uri", $event.source.uri);
      |                                      ^
C:/web-app/dukancard-app/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:39:3: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   39 |   $payload.setProperty(runtime, "source", source);
      |   ^
C:/web-app/dukancard-app/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:41:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   41 |     return $payload;
      |            ^
9 warnings generated.
[70/97] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/144b43cb78c34782fe09b4d2b440768b/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/States.cpp.o
[71/97] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/e8f14cb3b97a36338467b80bba742990/build/generated/source/codegen/jni/react/renderer/components/rnsvg/rnsvgJSI-generated.cpp.o
[72/97] Linking CXX shared library C:\web-app\dukancard-app\android\app\build\intermediates\cxx\Debug\i6d1eg1k\obj\x86\libreact_codegen_rnscreens.so
[73/97] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/144b43cb78c34782fe09b4d2b440768b/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/Props.cpp.o
[74/97] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/144b43cb78c34782fe09b4d2b440768b/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/ShadowNodes.cpp.o
[75/97] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/C_/web-app/dukancard-app/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGImageShadowNode.cpp.o
[76/97] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/e8f14cb3b97a36338467b80bba742990/build/generated/source/codegen/jni/react/renderer/components/rnsvg/ComponentDescriptors.cpp.o
[77/97] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/C_/web-app/dukancard-app/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGImageState.cpp.o
[78/97] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/C_/web-app/dukancard-app/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGLayoutableShadowNode.cpp.o
[79/97] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/C_/web-app/dukancard-app/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGShadowNodes.cpp.o
[80/97] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/rnsvg.cpp.o
[81/97] Building CXX object RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/RNCWebViewSpec-generated.cpp.o
[82/97] Linking CXX shared library C:\web-app\dukancard-app\android\app\build\intermediates\cxx\Debug\i6d1eg1k\obj\x86\libreact_codegen_rnsvg.so
[83/97] Building CXX object RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ComponentDescriptors.cpp.o
[84/97] Building CXX object RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/EventEmitters.cpp.o
[85/97] Building CXX object RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/RNEdgeToEdge-generated.cpp.o
[86/97] Building CXX object RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/Props.cpp.o
[87/97] Building CXX object RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ShadowNodes.cpp.o
[88/97] Building CXX object RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ComponentDescriptors.cpp.o
[89/97] Building CXX object RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/RNCWebViewSpecJSI-generated.cpp.o
[90/97] Building CXX object RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/States.cpp.o
[91/97] Building CXX object RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/EventEmitters.cpp.o
[92/97] Building CXX object CMakeFiles/appmodules.dir/C_/web-app/dukancard-app/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o
[93/97] Building CXX object RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/Props.cpp.o
[94/97] Building CXX object RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/RNEdgeToEdgeJSI-generated.cpp.o
[95/97] Building CXX object RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ShadowNodes.cpp.o
[96/97] Building CXX object RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/States.cpp.o
[97/97] Linking CXX shared library C:\web-app\dukancard-app\android\app\build\intermediates\cxx\Debug\i6d1eg1k\obj\x86\libappmodules.so
